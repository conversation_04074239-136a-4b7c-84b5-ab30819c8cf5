"""
Warning suppression module - must be imported first to suppress all warnings
"""
import warnings
import os

# Disable all warnings globally
warnings.filterwarnings("ignore")

# Set environment variables to suppress SSL warnings
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['CURL_CA_BUNDLE'] = ''

# Disable urllib3 warnings specifically
try:
    import urllib3
    urllib3.disable_warnings()
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    # Also disable at the warnings level
    warnings.filterwarnings("ignore", category=urllib3.exceptions.InsecureRequestWarning)
except (ImportError, AttributeError):
    pass

# Disable httpx warnings
try:
    import httpx  # noqa: F401
    # Suppress any httpx related warnings
    warnings.filterwarnings("ignore", module="httpx")
except ImportError:
    pass

# Disable requests warnings
try:
    import requests
    requests.packages.urllib3.disable_warnings()
except (ImportError, AttributeError):
    pass

# Additional comprehensive warning suppression
warnings.filterwarnings("ignore", message=".*InsecureRequestWarning.*")
warnings.filterwarnings("ignore", message=".*Unverified HTTPS request.*")
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# Monkey patch warnings.warn to completely suppress warnings
original_warn = warnings.warn

def silent_warn(*args, **kwargs):
    """Silently ignore all warnings"""
    pass

warnings.warn = silent_warn
