# tabs/ticket_type_selection_tab.py
import logging
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, 
    QCheckBox,QAbstractItemView, QPushButton
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor

logger = logging.getLogger("webook_pro")

class TicketTypeSelectionTab(QWidget):
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.selected_types = {}  # ttype -> bool
        self.tickets_info = {}    # ttype -> {status: {seat_id: data}}
        self.auto_hold_enabled = False
        self.hold_all_enabled = False  # New flag for hold all mode
        self.type_to_row = {}
        self.previous_counts = {}  # Track previous free counts
        self.status_colors = {    # Define colors for different statuses
            'free': QColor(240, 255, 240),
            'reservedByToken': QColor(255, 245, 230),
            'booked': QColor(255, 230, 230),
            'held': QColor(230, 230, 255)
        }

        layout = QVBoxLayout()
        top_layout = QHBoxLayout()
        
        self.filter_edit = QLineEdit()
        self.filter_edit.setPlaceholderText("Filter ticket types...")
        self.filter_edit.textChanged.connect(self.update_table_data)
        top_layout.addWidget(self.filter_edit)

        self.auto_hold_checkbox = QCheckBox("Auto-Hold New Seats")
        self.auto_hold_checkbox.setChecked(False)
        self.auto_hold_checkbox.stateChanged.connect(self.on_auto_hold_changed)
        top_layout.addWidget(self.auto_hold_checkbox)
        
        # Add hold all toggle
        self.hold_all_checkbox = QCheckBox("Hold All Free Seats")
        self.hold_all_checkbox.setChecked(False)
        self.hold_all_checkbox.setToolTip("Hold any free seat regardless of type selection")
        self.hold_all_checkbox.stateChanged.connect(self.on_hold_all_changed)
        top_layout.addWidget(self.hold_all_checkbox)
        
        # Add reset button
        self.reset_button = QPushButton("Reset Auto-Hold")
        self.reset_button.setToolTip("Reset auto-hold tracking to make failed seats eligible again")
        self.reset_button.clicked.connect(self.on_reset_auto_hold)
        top_layout.addWidget(self.reset_button)

        # Add debug checkbox
        self.debug_checkbox = QCheckBox("Debug Mode")
        self.debug_checkbox.setChecked(False)
        self.debug_checkbox.setToolTip("Show detailed request/response logs for debugging")
        self.debug_checkbox.stateChanged.connect(self.on_debug_mode_changed)
        top_layout.addWidget(self.debug_checkbox)

        layout.addLayout(top_layout)

        self.table = QTableWidget()
        self.table.setColumnCount(4)  # Added column for status breakdown
        self.table.setHorizontalHeaderLabels(["Use?", "Ticket Type", "Available", "Status Breakdown"])
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        layout.addWidget(self.table)

        self.setLayout(layout)

    def on_reset_auto_hold(self):
        """Handle reset button clicks"""
        if hasattr(self.main_window, 'reset_auto_held_seats'):
            self.main_window.reset_auto_held_seats()

    def on_debug_mode_changed(self, state):
        """Handle debug mode checkbox changes"""
        debug_enabled = (state == Qt.Checked)

        # Import here to avoid circular imports
        try:
            from auto_hold import set_debug_mode
            success = set_debug_mode(debug_enabled)

            if success:
                status = "ENABLED" if debug_enabled else "DISABLED"
                self.main_window.log(f"🔍 Debug Mode: {status}")
                if debug_enabled:
                    self.main_window.log("⚠️  Debug mode will show detailed request/response logs - may impact performance")
            else:
                self.main_window.log("❌ Failed to set debug mode - auto-hold system not initialized")
                # Reset checkbox if failed
                self.debug_checkbox.setChecked(False)

        except ImportError as e:
            self.main_window.log(f"❌ Failed to import debug functions: {str(e)}")
            self.debug_checkbox.setChecked(False)

    def update_tickets_info(self, tickets_info: dict):
        """Update with new seat data from main window"""
        # Store previous free counts for visual feedback
        self.previous_counts = {
            ttype: len(data.get('free', {}))
            for ttype, data in self.tickets_info.items()
        }
        
        # Preserve selections through updates
        previous_selections = self.selected_types.copy()
        self.tickets_info = tickets_info
        
        # Initialize new types with unchecked state
        for ttype in tickets_info:
            if ttype not in self.selected_types:
                self.selected_types[ttype] = False
                
        self.update_table_data()

    def update_table_data(self):
        """Rebuild table based on current filter and data"""
        filter_text = self.filter_edit.text().strip().lower()
        all_types = sorted(self.tickets_info.keys())
        filtered_types = [t for t in all_types if filter_text in t.lower()]

        self.table.setRowCount(len(filtered_types))
        self.type_to_row.clear()

        for row_idx, ttype in enumerate(filtered_types):
            self.type_to_row[ttype] = row_idx
            
            # Checkbox column
            checkbox = QCheckBox()
            checkbox.setChecked(self.selected_types.get(ttype, False))
            checkbox.stateChanged.connect(lambda state, t=ttype: self.on_checkbox_changed(state, t))
            self.table.setCellWidget(row_idx, 0, checkbox)

            # Type name column
            type_item = QTableWidgetItem(ttype)
            type_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
            self.table.setItem(row_idx, 1, type_item)

            # Available (free) count column
            free_count = len(self.tickets_info.get(ttype, {}).get('free', {}))
            avail_item = QTableWidgetItem(str(free_count))
            avail_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
            self.table.setItem(row_idx, 2, avail_item)

            # Status breakdown column
            status_counts = []
            for status, color in self.status_colors.items():
                count = len(self.tickets_info.get(ttype, {}).get(status, {}))
                if count > 0:
                    status_counts.append(f"{status}: {count}")
            breakdown_item = QTableWidgetItem(", ".join(status_counts))
            breakdown_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
            self.table.setItem(row_idx, 3, breakdown_item)

            # Store initial count
            self.previous_counts[ttype] = free_count

    def update_type_row(self, ttype):
        """Update specific row with latest data and visual feedback"""
        try:
            if ttype not in self.type_to_row:
                return  # Type not in current view

            row_idx = self.type_to_row[ttype]
            current_free = len(self.tickets_info.get(ttype, {}).get('free', {}))
            previous_free = self.previous_counts.get(ttype, 0)

            # Update available count
            avail_item = self.table.item(row_idx, 2)
            if avail_item:
                avail_item.setText(str(current_free))
                
                # Visual feedback
                if current_free > previous_free:
                    self.flash_row(row_idx, QColor(200, 255, 200))  # Green
                elif current_free < previous_free:
                    self.flash_row(row_idx, QColor(255, 200, 200))  # Red

            # Update status breakdown
            status_counts = []
            for status, color in self.status_colors.items():
                count = len(self.tickets_info.get(ttype, {}).get(status, {}))
                if count > 0:
                    status_counts.append(f"{status}: {count}")
            breakdown_item = self.table.item(row_idx, 3)
            if breakdown_item:
                breakdown_item.setText(", ".join(status_counts))

            # Update stored count
            self.previous_counts[ttype] = current_free

        except Exception as e:
            logger.error(f"Error updating row {ttype}: {str(e)}")

    def flash_row(self, row_idx, color):
        """Flash entire row with specified color"""
        for col in [1, 2, 3]:  # Type, Available, Breakdown columns
            item = self.table.item(row_idx, col)
            if item:
                item.setBackground(color)
                
        # Reset after 500ms
        QTimer.singleShot(500, lambda: self.reset_row_color(row_idx))

    def reset_row_color(self, row_idx):
        """Reset row to default colors based on status"""
        ttype = self.table.item(row_idx, 1).text()
        for col in [1, 2, 3]:
            item = self.table.item(row_idx, col)
            if item:
                # Apply status-based background colors
                if col == 3:  # Status breakdown column
                    item.setBackground(QColor(255, 255, 255))
                else:
                    item.setBackground(QColor(255, 255, 255))

    def on_checkbox_changed(self, state, ttype):
        """Handle checkbox state changes"""
        self.selected_types[ttype] = (state == Qt.Checked)
        self.main_window.log(f"Type '{ttype}' selection: {'ON' if state else 'OFF'}")

    def get_selected_types(self):
        """Get list of selected ticket types"""
        if self.hold_all_enabled:
            return ['*']  # Special wildcard to indicate all types
        return [t for t, selected in self.selected_types.items() if selected]

    def get_available_seats_from_selection(self, max_seats=None):
        """Get available (free) seats from selected ticket types"""
        available_seats = []
        selected_types = self.get_selected_types()

        if not selected_types:
            return available_seats

        # If holding all types
        if '*' in selected_types:
            for ttype, statuses in self.tickets_info.items():
                free_seats = statuses.get('free', {})
                available_seats.extend(list(free_seats.keys()))
        else:
            # Only from selected types
            for ttype in selected_types:
                if ttype in self.tickets_info:
                    free_seats = self.tickets_info[ttype].get('free', {})
                    available_seats.extend(list(free_seats.keys()))

        # Limit to max_seats if specified
        if max_seats and len(available_seats) > max_seats:
            available_seats = available_seats[:max_seats]

        return available_seats

    def on_auto_hold_changed(self, state):
        """Handle auto-hold checkbox changes"""
        self.auto_hold_enabled = (state == Qt.Checked)
        status = "ENABLED" if self.auto_hold_enabled else "DISABLED"
        self.main_window.log(f"Auto-Hold: {status}")

    def on_hold_all_changed(self, state):
        """Handle hold-all checkbox changes"""
        self.hold_all_enabled = (state == Qt.Checked)
        status = "ENABLED" if self.hold_all_enabled else "DISABLED"
        self.main_window.log(f"Hold All Mode: {status}")
        
        # Update UI state
        if self.hold_all_enabled:
            self.filter_edit.setEnabled(False)
            self.table.setEnabled(False)
        else:
            self.filter_edit.setEnabled(True)
            self.table.setEnabled(True)
