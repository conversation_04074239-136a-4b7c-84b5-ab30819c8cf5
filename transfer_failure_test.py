#!/usr/bin/env python3
"""
Transfer Failure Test
Specifically tests the failing transfer scenarios to identify the root cause
"""

import asyncio
import json
import time
import logging
from typing import Optional, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_failing_seats():
    """Test the specific seats that are failing in transfers"""
    
    # The failing seats from your logs
    failing_seats = ["D7-P-4", "D7-P-5", "D7-P-6", "D7-P-8", "D7-P-9", "D7-Q-1", "D7-Q-2"]
    
    logger.info("🧪 Testing failing seat transfers...")
    logger.info(f"📋 Testing seats: {failing_seats}")
    
    # Import required functions
    try:
        from helper import async_make_request
        from chart_token_manager import generate_x_signature
        from account_token_manager import get_account_tokens
        from proxy_manager import get_proxy_string
    except ImportError as e:
        logger.error(f"❌ Failed to import required modules: {e}")
        return
    
    # Get system configuration
    proxy = get_proxy_string()
    account_tokens = await get_account_tokens()
    
    if not account_tokens:
        logger.error("❌ No account tokens available")
        return
    
    logger.info(f"✅ Using account token: {account_tokens[0][:20]}...")
    if proxy:
        parts = proxy.split(':')
        logger.info(f"✅ Using proxy: {parts[0]}:{parts[1]} (User: {parts[2]})")
    
    # Test configuration
    seatsio_ip = "*************"
    event_key = "al-fateh-vs-al-fayha-spl-25-26-m-w-1"
    chart_key = "06744f62-aa66-4c18-845a-b3baddf41402"
    
    # Endpoints
    token_endpoint = f'https://{seatsio_ip}/chart.js'
    hold_endpoint = f'https://{seatsio_ip}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
    release_endpoint = f'https://{seatsio_ip}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-objects'
    
    results = []
    
    for seat_id in failing_seats[:3]:  # Test first 3 seats to avoid overwhelming
        logger.info(f"\n🎯 Testing seat: {seat_id}")
        logger.info("-" * 40)
        
        seat_result = {
            'seat_id': seat_id,
            'token_generation': None,
            'initial_hold': None,
            'release': None,
            'transfer_hold': None,
            'overall_success': False
        }
        
        try:
            # Step 1: Generate hold token
            logger.info("Step 1: Generating hold token...")
            
            token_headers = {
                'Host': 'cdn-eu.seatsio.net',
                'accept': 'application/json',
                'authorization': f'Bearer {account_tokens[0]}',
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            token_payload = {
                'chartKey': chart_key,
                'event': event_key,
                'expiry': 15
            }
            
            start_time = time.perf_counter()
            token_response = await async_make_request(
                'POST', token_endpoint, 
                proxy=proxy, 
                json=token_payload, 
                headers=token_headers
            )
            token_time = (time.perf_counter() - start_time) * 1000
            
            seat_result['token_generation'] = {
                'status_code': token_response.status_code,
                'timing_ms': f"{token_time:.2f}",
                'success': token_response.status_code == 200
            }
            
            if token_response.status_code != 200:
                logger.error(f"❌ Token generation failed: {token_response.status_code} - {token_response.text[:200]}")
                seat_result['token_generation']['error'] = token_response.text[:200]
                results.append(seat_result)
                continue
            
            token_data = token_response.json()
            hold_token = token_data.get('holdToken')
            
            if not hold_token:
                logger.error(f"❌ No holdToken in response: {token_data}")
                seat_result['token_generation']['error'] = "No holdToken in response"
                results.append(seat_result)
                continue
            
            logger.info(f"✅ Token generated: {hold_token[:8]}... ({token_time:.2f}ms)")
            
            # Step 2: Hold seat with first token
            logger.info("Step 2: Holding seat with generated token...")
            
            hold_headers = {
                'Host': 'cdn-eu.seatsio.net',
                'accept': '*/*',
                'content-type': 'application/json',
                'origin': 'https://cdn-eu.seatsio.net',
                'x-client-tool': 'Renderer',
                'x-browser-id': f'test-{int(time.time())}'
            }
            
            hold_payload = {
                'events': [event_key],
                'holdToken': hold_token,
                'objects': [{'objectId': seat_id}],
                'channelKeys': ['NO_CHANNEL'],
                'validateEventsLinkedToSameChart': True
            }
            
            hold_body = json.dumps(hold_payload, separators=(',', ':'))
            hold_headers['x-signature'] = generate_x_signature(hold_body)
            
            start_time = time.perf_counter()
            hold_response = await async_make_request(
                'POST', hold_endpoint,
                proxy=proxy,
                content=hold_body,
                headers=hold_headers
            )
            hold_time = (time.perf_counter() - start_time) * 1000
            
            seat_result['initial_hold'] = {
                'status_code': hold_response.status_code,
                'timing_ms': f"{hold_time:.2f}",
                'success': hold_response.status_code == 204,
                'request_body': hold_body,
                'response_body': hold_response.text
            }
            
            if hold_response.status_code != 204:
                logger.error(f"❌ Initial hold failed: {hold_response.status_code} - {hold_response.text[:200]}")
                results.append(seat_result)
                continue
            
            logger.info(f"✅ Seat held successfully ({hold_time:.2f}ms)")
            
            # Step 3: Generate second token for transfer
            logger.info("Step 3: Generating second token for transfer...")
            
            start_time = time.perf_counter()
            token2_response = await async_make_request(
                'POST', token_endpoint,
                proxy=proxy,
                json=token_payload,
                headers=token_headers
            )
            token2_time = (time.perf_counter() - start_time) * 1000
            
            if token2_response.status_code != 200:
                logger.error(f"❌ Second token generation failed: {token2_response.status_code}")
                results.append(seat_result)
                continue
            
            token2_data = token2_response.json()
            hold_token2 = token2_data.get('holdToken')
            logger.info(f"✅ Second token generated: {hold_token2[:8]}... ({token2_time:.2f}ms)")
            
            # Step 4: Release from first token
            logger.info("Step 4: Releasing seat from first token...")
            
            release_payload = {
                'events': [event_key],
                'holdToken': hold_token,
                'objects': [{'objectId': seat_id}],
                'channelKeys': ['NO_CHANNEL'],
                'validateEventsLinkedToSameChart': True
            }
            
            release_body = json.dumps(release_payload, separators=(',', ':'))
            release_headers = hold_headers.copy()
            release_headers['x-signature'] = generate_x_signature(release_body)
            
            start_time = time.perf_counter()
            release_response = await async_make_request(
                'POST', release_endpoint,
                proxy=proxy,
                content=release_body,
                headers=release_headers
            )
            release_time = (time.perf_counter() - start_time) * 1000
            
            seat_result['release'] = {
                'status_code': release_response.status_code,
                'timing_ms': f"{release_time:.2f}",
                'success': release_response.status_code == 204,
                'request_body': release_body,
                'response_body': release_response.text
            }
            
            logger.info(f"Release result: {release_response.status_code} ({release_time:.2f}ms)")
            if release_response.status_code != 204:
                logger.warning(f"⚠️ Release failed: {release_response.text[:200]}")
            
            # Step 5: Hold with second token (the critical transfer step)
            logger.info("Step 5: Holding seat with second token (transfer)...")
            
            transfer_payload = {
                'events': [event_key],
                'holdToken': hold_token2,
                'objects': [{'objectId': seat_id}],
                'channelKeys': ['NO_CHANNEL'],
                'validateEventsLinkedToSameChart': True
            }
            
            transfer_body = json.dumps(transfer_payload, separators=(',', ':'))
            transfer_headers = hold_headers.copy()
            transfer_headers['x-signature'] = generate_x_signature(transfer_body)
            
            start_time = time.perf_counter()
            transfer_response = await async_make_request(
                'POST', hold_endpoint,
                proxy=proxy,
                content=transfer_body,
                headers=transfer_headers
            )
            transfer_time = (time.perf_counter() - start_time) * 1000
            
            seat_result['transfer_hold'] = {
                'status_code': transfer_response.status_code,
                'timing_ms': f"{transfer_time:.2f}",
                'success': transfer_response.status_code == 204,
                'request_body': transfer_body,
                'response_body': transfer_response.text
            }
            
            if transfer_response.status_code == 204:
                logger.info(f"✅ Transfer successful! ({transfer_time:.2f}ms)")
                seat_result['overall_success'] = True
            else:
                logger.error(f"❌ Transfer failed: {transfer_response.status_code} - {transfer_response.text[:200]}")
            
        except Exception as e:
            logger.error(f"❌ Exception testing seat {seat_id}: {str(e)}")
            seat_result['exception'] = str(e)
        
        results.append(seat_result)
        
        # Small delay between seats
        await asyncio.sleep(1)
    
    # Print summary
    logger.info("\n" + "=" * 60)
    logger.info("🎯 TRANSFER FAILURE TEST SUMMARY")
    logger.info("=" * 60)
    
    for result in results:
        seat_id = result['seat_id']
        success = result['overall_success']
        status = "✅ SUCCESS" if success else "❌ FAILED"
        
        logger.info(f"\n🎫 Seat {seat_id}: {status}")
        
        if result.get('token_generation'):
            tg = result['token_generation']
            logger.info(f"   Token Gen: {tg['status_code']} ({tg.get('timing_ms', 'N/A')}ms)")
        
        if result.get('initial_hold'):
            ih = result['initial_hold']
            logger.info(f"   Initial Hold: {ih['status_code']} ({ih.get('timing_ms', 'N/A')}ms)")
        
        if result.get('release'):
            rel = result['release']
            logger.info(f"   Release: {rel['status_code']} ({rel.get('timing_ms', 'N/A')}ms)")
        
        if result.get('transfer_hold'):
            th = result['transfer_hold']
            logger.info(f"   Transfer Hold: {th['status_code']} ({th.get('timing_ms', 'N/A')}ms)")
            if not th['success'] and th.get('response_body'):
                logger.info(f"   Transfer Error: {th['response_body'][:200]}")
    
    # Save detailed results
    with open('transfer_failure_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"\n💾 Detailed results saved to transfer_failure_results.json")
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_failing_seats())
