# tabs/held_seats_tab.py - Updated to work with TokenManagementSystem
import logging
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QAbstractItemView, QFormLayout,
    QLabel, QMessageBox, QCheckBox, QProgressDialog
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
import threading
import time

logger = logging.getLogger("webook_pro")

class TransferWorkerSignals(QObject):
    """Signals for the transfer worker"""
    progress = pyqtSignal(int, int)  # current, total
    seat_result = pyqtSignal(str, bool)  # seat_id, success
    finished = pyqtSignal(int)  # total transferred

class HeldSeatsTab(QWidget):
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.selected_seats = {}
        self.seat_row_map = {}  # seat_label -> row index
        self.last_update_time = 0
        self.cached_data = []
        self.filtered_data = []
        self.transfer_in_progress = False
        self.transfer_signals = TransferWorkerSignals()
        self.transfer_signals.progress.connect(self._update_transfer_progress)
        self.transfer_signals.seat_result.connect(self._handle_seat_transfer_result)
        self.transfer_signals.finished.connect(self._handle_transfer_finished)
        self.current_sort_column = 0  # Default sort by seat label
        self.current_sort_order = Qt.AscendingOrder  # Default sort order

        layout = QVBoxLayout()
        
        # Filter setup
        top_layout = QHBoxLayout()
        self.filter_edit = QLineEdit()
        self.filter_edit.setPlaceholderText("Filter seats/token/team...")
        self.filter_edit.textChanged.connect(self.apply_filter)
        top_layout.addWidget(self.filter_edit)
        
        # Auto-refresh toggle
        self.auto_refresh_check = QCheckBox("Auto Refresh")
        self.auto_refresh_check.setChecked(True)
        top_layout.addWidget(self.auto_refresh_check)
        
        # Select all checkbox
        self.select_all_check = QCheckBox("Select All")
        self.select_all_check.stateChanged.connect(self.on_select_all_changed)
        top_layout.addWidget(self.select_all_check)
        
        layout.addLayout(top_layout)

        # Table setup
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["Seat Label", "Token", "Team ID", "Time Left", "Select"])
        self.table.setSelectionMode(QAbstractItemView.NoSelection)
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table.itemChanged.connect(self.on_item_changed)
        
        # Enable sorting
        self.table.setSortingEnabled(True)
        self.table.horizontalHeader().setSortIndicatorShown(True)
        self.table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        
        layout.addWidget(self.table)

        # Transfer controls
        btn_layout = QHBoxLayout()
        form = QFormLayout()
        self.user_hold_token_edit = QLineEdit()
        form.addRow(QLabel("Your Hold Token:"), self.user_hold_token_edit)
        btn_layout.addLayout(form)

        self.transfer_btn = QPushButton("Transfer Selected")
        self.transfer_btn.clicked.connect(self.transfer_selected_seats)
        btn_layout.addWidget(self.transfer_btn)
        
        # Add Stop button
        self.stop_transfer_btn = QPushButton("Stop Transfer")
        self.stop_transfer_btn.setEnabled(False)
        self.stop_transfer_btn.clicked.connect(self.stop_transfer)
        btn_layout.addWidget(self.stop_transfer_btn)
        
        layout.addLayout(btn_layout)

        self.setLayout(layout)
        
        # Setup refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.incremental_refresh)
        self.refresh_timer.start(1000)  # Update every second

    def on_header_clicked(self, logical_index):
        """Handle table header clicks for sorting"""
        # Toggle sort order if clicking the same column
        if self.current_sort_column == logical_index:
            self.current_sort_order = Qt.DescendingOrder if self.current_sort_order == Qt.AscendingOrder else Qt.AscendingOrder
        else:
            self.current_sort_column = logical_index
            self.current_sort_order = Qt.AscendingOrder
        
        # Set the sort indicator
        self.table.horizontalHeader().setSortIndicator(self.current_sort_column, self.current_sort_order)
        
        # Perform the sort (rebuilding the table with the new sort order)
        self._sort_data()
        self.update_table_structure()

    def _sort_data(self):
        """Sort the filtered data based on current sort column and order"""
        # Define column name mappings (0=seat_label, 1=token, 2=team_id, 3=time_left)
        column_keys = ['seat_label', 'token', 'team_id', 'time_left']
        
        # Make sure current_sort_column is valid
        if self.current_sort_column < 0 or self.current_sort_column >= len(column_keys):
            return
            
        # Don't sort checkbox column (column 4)
        if self.current_sort_column == 4:
            return
        
        # Get the sort key
        sort_key = column_keys[self.current_sort_column]
        
        # Define sort function
        def sort_func(seat):
            # Special handling for time_left (numeric sort)
            if sort_key == 'time_left':
                return int(seat.get(sort_key, 0))
            # Default string sort
            return str(seat.get(sort_key, ''))
        
        # Sort the list
        reverse = self.current_sort_order == Qt.DescendingOrder
        self.filtered_data.sort(key=sort_func, reverse=reverse)

    def refresh_data(self):
        """Refresh data from token management system"""
        # Only use the token_system, no fallback to token_managers
        if hasattr(self.main_window, 'token_system') and self.main_window.token_system:
            # Get all tokens from token_system
            token_ids = self.main_window.token_system.get_all_tokens()
            
            # Build data structure
            data = []
            for token_id in token_ids:
                # Get token details
                token_status = self.main_window.token_system.get_token_status(token_id)
                if not token_status:
                    continue
                    
                # Add entry for each seat
                team_id = self.main_window.team_combo.currentData() or "No Team"
                for seat_label in token_status.get('seats', []):
                    data.append({
                        'seat_label': seat_label,
                        'token': token_id,
                        'team_id': team_id,
                        'expire_time': token_status['expire_time'],
                        'time_left': token_status['time_left'],
                        'is_renewing': token_status['is_renewing']
                    })
        else:
            # No token system, return empty data
            data = []
            logger.warning("No TokenManagementSystem found, no seats to display")
        
        # Update cached data
        self.cached_data = data
        
        # Apply filter and update UI
        self.apply_filter()


    def on_select_all_changed(self, state):
        """Handle select all checkbox changes"""
        checked = state == Qt.Checked
        
        # Update all checkboxes in the table
        self.table.blockSignals(True)
        for row in range(self.table.rowCount()):
            check_item = self.table.item(row, 4)
            if check_item:
                check_item.setCheckState(Qt.Checked if checked else Qt.Unchecked)
                
                # Also update selection tracking
                seat_id = self.table.item(row, 0).text()
                self.selected_seats[seat_id] = checked
                
        self.table.blockSignals(False)

    def on_item_changed(self, item):
        if item.column() == 4 and item.row() in self.seat_row_map.values():
            seat_label = self.table.item(item.row(), 0).text()
            self.selected_seats[seat_label] = item.checkState() == Qt.Checked

    def rebuild_base_data(self):
        """Cache all held seats data (called when structure changes)"""
        # Only use the token_system, no fallback to token_managers
        if hasattr(self.main_window, 'token_system') and self.main_window.token_system:
            # Get all tokens from token_system
            token_system = self.main_window.token_system
            token_ids = token_system.get_all_tokens()
            
            # Build data structure
            data = []
            
            # Special handling to reduce chance of deadlocks
            # Create a snapshot of the token data with minimal locking
            token_data = {}
            with token_system.token_lock:
                for token_id in token_ids:
                    if token_id in token_system.tokens:
                        token_data[token_id] = token_system.tokens[token_id].copy()
            
            # Create a snapshot of seat data with minimal locking
            seat_to_token = {}
            token_to_seats = {}
            with token_system.seat_lock:
                seat_to_token = token_system.seat_to_token.copy()
                for token_id in token_ids:
                    if token_id in token_system.token_to_seats:
                        token_to_seats[token_id] = set(token_system.token_to_seats[token_id])
            
            # Now build the data structure without holding locks
            for token_id in token_ids:
                # Get token details from our snapshot
                if token_id not in token_data:
                    continue
                    
                token_info = token_data[token_id]
                
                # Calculate current time left
                current_time = time.time()
                time_left = max(0, int(token_info.get("expire_time", 0) - current_time))
                
                # Get seats for this token from our snapshot
                seats = token_to_seats.get(token_id, set())
                
                # Add entry for each seat
                team_id = self.main_window.team_combo.currentData() or "No Team"
                for seat_label in seats:
                    # Extra validation - confirm seat still belongs to this token
                    if seat_to_token.get(seat_label) != token_id:
                        continue
                        
                    data.append({
                        'seat_label': seat_label,
                        'token': token_id,
                        'team_id': team_id,
                        'expire_time': token_info.get('expire_time', 0),
                        'time_left': time_left,
                        'is_renewing': token_info.get('is_renewing', False)
                    })
        else:
            # No token system, return empty data
            data = []
        
        # Update cached data
        self.cached_data = data
        
        # Apply filter and update UI
        self.apply_filter()
        
        # Record update time
        self.last_update_time = time.time()
        
    def update_time_item(self, row_idx, time_item, current_time=None):
        """Update time display for a single row"""
        current_time = current_time or self.main_window.get_now()
        
        if row_idx >= 0 and row_idx < self.table.rowCount():
            seat_label = self.table.item(row_idx, 0).text()
            token = self.table.item(row_idx, 1).text()
            
            # Only use token_system
            if hasattr(self.main_window, 'token_system') and self.main_window.token_system:
                token_status = self.main_window.token_system.get_token_status(token)
                if token_status:
                    time_left = token_status['time_left']
                    mm = time_left // 60
                    ss = time_left % 60
                    time_str = f"{mm:02d}:{ss:02d}"
                    if time_item.text() != time_str:
                        time_item.setText(time_str)
                    return
                    
            # Token not found or no token system
            time_item.setText("EXPIRED")

    def apply_filter(self):
        """Apply current filter to cached data and maintain sorting"""
        filter_text = self.filter_edit.text().lower()
        self.filtered_data = [
            seat for seat in self.cached_data
            if filter_text in f"{seat['seat_label']} {seat['token']} {seat['team_id']}".lower()
        ]
        
        # Apply current sorting
        self._sort_data()
        
        # Update table
        self.update_table_structure()

    def update_table_structure(self):
        """Full table rebuild (only when data structure changes)"""
        self.table.blockSignals(True)
        self.table.clearContents()
        self.table.setRowCount(len(self.filtered_data))
        self.seat_row_map.clear()

        for row_idx, seat in enumerate(self.filtered_data):
            self.seat_row_map[seat['seat_label']] = row_idx
            
            # Seat Label
            self.table.setItem(row_idx, 0, QTableWidgetItem(seat['seat_label']))
            
            # Token
            self.table.setItem(row_idx, 1, QTableWidgetItem(seat['token']))
            
            # Team ID
            self.table.setItem(row_idx, 2, QTableWidgetItem(seat['team_id']))
            
            # Time Left
            time_item = QTableWidgetItem()
            self.update_time_item(row_idx, time_item)
            self.table.setItem(row_idx, 3, time_item)
            
            # Checkbox
            cb_item = QTableWidgetItem()
            cb_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            cb_item.setCheckState(Qt.Checked if self.selected_seats.get(seat['seat_label'], False) else Qt.Unchecked)
            self.table.setItem(row_idx, 4, cb_item)

        self.table.blockSignals(False)

    def incremental_refresh(self):
        """Update time columns and check seat validity"""
        if not self.auto_refresh_check.isChecked():
            return

        current_time = self.main_window.get_now()
        
        # Save scroll position
        scrollbar = self.table.verticalScrollBar()
        scroll_pos = scrollbar.value()
        
        rows_to_remove = []

        for row_idx in range(self.table.rowCount()):
            seat_label_item = self.table.item(row_idx, 0)
            if not seat_label_item:
                continue

            seat_label = seat_label_item.text()
            time_item = self.table.item(row_idx, 3)
            
            # Check if seat still exists
            if not self.seat_exists(seat_label):
                rows_to_remove.append(row_idx)
                continue
                
            # Update time display
            self.update_time_item(row_idx, time_item, current_time)

        # Remove invalid rows
        if rows_to_remove:
            for row in sorted(rows_to_remove, reverse=True):
                seat_label = self.table.item(row, 0).text()
                if seat_label in self.seat_row_map:
                    del self.seat_row_map[seat_label]
                self.table.removeRow(row)
            
            # Rebuild seat_row_map after removals
            self.seat_row_map = {self.table.item(row, 0).text(): row 
                                for row in range(self.table.rowCount())}
        
        # Restore scroll position
        scrollbar.setValue(scroll_pos)

    def seat_exists(self, seat_label):
        """Check if seat still exists in token system"""
        # Only use token_system
        if hasattr(self.main_window, 'token_system') and self.main_window.token_system:
            all_seats = self.main_window.token_system.get_all_seats()
            return seat_label in all_seats
        
        # No token system
        return False

    def update_time_displays_only(self):
        """
        Updates only the time displays without rebuilding the entire table.
        This prevents UI reset during token time refreshes.
        """
        current_time = self.main_window.get_now()
        
        # Save scroll position
        scrollbar = self.table.verticalScrollBar()
        scroll_pos = scrollbar.value()
        
        # Update each row's time
        for row_idx in range(self.table.rowCount()):
            time_item = self.table.item(row_idx, 3)  # Time column
            if time_item:
                self.update_time_item(row_idx, time_item, current_time)
        
        # Restore scroll position
        scrollbar.setValue(scroll_pos)

    def auto_refresh_held_seats(self, force_rebuild=False):
        """
        Handle external refresh requests with option to force full rebuild.
        For token time updates, we don't need a full rebuild.
        """
        if force_rebuild:
            # Full rebuild (for structural changes)
            self.rebuild_base_data()
        else:
            # Check if we should force a data reload
            current_time = time.time()
            if current_time - self.last_update_time > 5:  # Force reload every 5 seconds
                self.rebuild_base_data()
                self.last_update_time = current_time
            else:
                # Just update times (faster, preserves state)
                self.update_time_displays_only()

    def _update_transfer_progress(self, current, total):
        """Update the progress dialog"""
        if self.progress_dialog:
            self.progress_dialog.setValue(current)
            
    def _handle_seat_transfer_result(self, seat_id, success):
        """Handle the result of a seat transfer"""
        # Update UI and log result
        if success:
            # Immediately remove from UI
            if seat_id in self.seat_row_map:
                row_idx = self.seat_row_map[seat_id]
                self.table.removeRow(row_idx)
                
                # Fix the mapping for remaining rows
                self.seat_row_map.pop(seat_id, None)
                
                # Update row indexes for rows after the removed one
                for s_id, r_idx in list(self.seat_row_map.items()):
                    if r_idx > row_idx:
                        self.seat_row_map[s_id] = r_idx - 1
                
                # Also remove from selected seats tracking
                self.selected_seats.pop(seat_id, None)
                
            self.main_window.log(f"Transferred seat {seat_id} to user token")
        else:
            self.main_window.log(f"Failed to transfer seat {seat_id}")
            
    def _handle_transfer_finished(self, transferred):
        """Clean up after transfer completes without progress dialog"""
        # Make sure we're really done
        self.transfer_in_progress = False
        self.stop_transfer_btn.setEnabled(False)
        
        # Log completion
        self.main_window.log(f"Transfer completed: {transferred} seats transferred successfully")
        
        # FORCE a complete UI rebuild to ensure all changes are reflected
        QTimer.singleShot(100, lambda: self.rebuild_base_data())
        
        # Schedule another refresh after a delay to catch any late updates
        QTimer.singleShot(500, lambda: self.rebuild_base_data())

    def get_selected_seats(self):
        """Get list of selected seat labels for booking"""
        selected_seats = []
        for row_idx in range(self.table.rowCount()):
            if self.table.item(row_idx, 4).checkState() == Qt.Checked:
                seat_label = self.table.item(row_idx, 0).text()
                selected_seats.append(seat_label)
        return selected_seats

    def transfer_selected_seats(self):
        """Transfer selected seats to user token without showing progress dialog"""
        if self.transfer_in_progress:
            QMessageBox.warning(self, "Warning", "Transfer already in progress.")
            return

        user_token = self.user_hold_token_edit.text().strip()
        if not user_token:
            QMessageBox.warning(self, "Warning", "No user token provided.")
            return

        # Get selected seats from current view
        selected = []
        for row_idx in range(self.table.rowCount()):
            if self.table.item(row_idx, 4).checkState() == Qt.Checked:
                seat_label = self.table.item(row_idx, 0).text()
                old_token = self.table.item(row_idx, 1).text()
                selected.append((seat_label, old_token))

        if not selected:
            QMessageBox.warning(self, "Warning", "No seats selected.")
            return
                
        # Confirm with user
        confirm = QMessageBox.question(
            self, 
            "Confirm Transfer",
            f"Transfer {len(selected)} seats to token {user_token}?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
                
        # Mark transfer as in progress
        self.transfer_in_progress = True
        self.stop_transfer_btn.setEnabled(True)
        
        # Track results
        self.transfer_results = {
            'total': len(selected),
            'completed': 0,
            'success': 0,
            'fail': 0
        }
        
        # Reset progress counter
        self.transfer_progress_counter = 0
        
        # Use token_system
        if hasattr(self.main_window, 'token_system') and self.main_window.token_system:
            # Queue each transfer operation separately
            for seat_id, old_token in selected:
                self.main_window.token_system.transfer_seat(seat_id, user_token)
            
            # Log the start
            self.main_window.log(f"Starting transfer of {len(selected)} seats to {user_token}")
            
            # Start timer to check status
            self.transfer_timer = QTimer()
            self.transfer_timer.timeout.connect(self.check_transfer_progress)
            self.transfer_timer.start(500)  # Check every 500ms
        else:
            # Only use token_system now, no fallback
            self.transfer_in_progress = False
            self.stop_transfer_btn.setEnabled(False)
            QMessageBox.warning(self, "Error", "Token system not initialized. Cannot transfer seats.")


    def check_transfer_progress(self):
        """Check progress of transfers without updating a dialog"""
        # This is called periodically to update the UI during transfers
        
        if not self.transfer_in_progress:
            if hasattr(self, 'transfer_timer') and self.transfer_timer.isActive():
                self.transfer_timer.stop()
            return
        
        # Fixed counter logic - don't try to be too clever with actual status tracking
        progress_counter = getattr(self, 'transfer_progress_counter', 0) + 1
        self.transfer_progress_counter = progress_counter
        total = self.transfer_results.get('total', 0)
        
        # Check if we've waited long enough for transfers to complete
        if progress_counter >= max(20, total * 2):  # Wait for at least 20 iterations
            if hasattr(self, 'transfer_timer') and self.transfer_timer.isActive():
                self.transfer_timer.stop()
            
            # Do a final refresh of the held seats UI
            self.auto_refresh_held_seats(force_rebuild=True)
            
            # Complete the transfer
            self._handle_transfer_finished(total)  # Just report the expected count
            return
            
        # Refresh UI occasionally during transfers to show progress
        if progress_counter % 4 == 0:  # Refresh every 4 iterations (2 seconds)
            self.auto_refresh_held_seats(force_rebuild=True)


    def stop_transfer(self):
        """Stop the current transfer operation"""
        if self.transfer_in_progress:
            if hasattr(self, 'stop_event'):
                self.stop_event.set()
            
            if hasattr(self, 'transfer_timer') and self.transfer_timer.isActive():
                self.transfer_timer.stop()
            
            # Always reset the flag
            self.transfer_in_progress = False
            self.stop_transfer_btn.setEnabled(False)
            
            self.main_window.log("Transfer operation cancelled")
            
            # Refresh UI
            self.rebuild_base_data()

    def _transfer_worker(self, seats_to_transfer, user_token, stop_event):
        """Background worker thread for transferring seats (legacy approach)"""
        event_key = self.main_window.webook_data['data']['seats_io']['event_key']
        channel_keys = self.main_window.webook_data['data']['channel_keys']
        transferred = 0
        total = len(seats_to_transfer)
        
        # Update progress
        self.transfer_signals.progress.emit(0, total)
        
        for i, (seat_label, old_token) in enumerate(seats_to_transfer):
            # Check if we should stop
            if stop_event.is_set():
                break
                
            # Find the token manager
            tm = next((tm for tm in self.main_window.token_managers 
                      if tm.token == old_token), None)
            if not tm:
                self.transfer_signals.seat_result.emit(seat_label, False)
                continue
                
            # Try to transfer the seat
            try:
                from helper import switch_seat_immediate
                success = switch_seat_immediate(
                    seat_number=seat_label,
                    event_key=event_key,
                    old_token=old_token,
                    new_token=user_token,
                    channel_keys=channel_keys,
                    team_id=self.main_window.team_combo.currentData(),
                    proxy=self.main_window.get_proxy_string()
                )
                
                # Handle result
                if success:
                    transferred += 1
                    tm.remove_seat(seat_label)
                    
                # Update progress
                self.transfer_signals.progress.emit(i + 1, total)
                self.transfer_signals.seat_result.emit(seat_label, success)
                
            except Exception as e:
                logger.error(f"Transfer error for seat {seat_label}: {str(e)}")
                self.transfer_signals.seat_result.emit(seat_label, False)
                
            # Small delay between operations to prevent overwhelming the system
            time.sleep(0.1)
            
        # Done
        self.transfer_signals.finished.emit(transferred)