# Webook Booking System - Clean Architecture

## System Overview

The system has **two main functionalities**:
1. **Manual Hold** - User clicks on seats to hold them individually
2. **Auto Hold** - System automatically holds seats when they become available

## Token Flow (EliteSoftworks → Webook → SeatsIO)

```
1. EliteSoftworks Account Tokens
   ↓ (account_token_manager.py)
   
2. Webook Hold Tokens  
   ↓ (token_retrieval.py + webook_client.py)
   
3. Seat Holding Requests
   ↓ (helper.py + auto_hold.py)
   
4. SeatsIO API
```

### Detailed Flow

1. **Account Token Retrieval** (`account_token_manager.py`)
   - Fetches account tokens from `http://elitesoftworks.com/webook_accounts_system`
   - Manages token pool with rotation and failure tracking
   - Provides valid account tokens for hold token requests

2. **Hold Token Generation** (`token_retrieval.py`)
   - Uses account tokens to call `webook_client.get_hold_token(event_id, access_token)`
   - Returns event-specific hold tokens
   - Hold tokens are used for actual seat holding operations

3. **Seat Holding** (`helper.py` + `auto_hold.py`)
   - Uses hold tokens to make POST requests to SeatsIO
   - Includes seat information and hold token in request
   - Returns success/failure status

## Core Components

### 1. Main Window (`main_window.py`)
- **Booking Tab**: Load events, manual seat holding, view seat maps
- **Preparation Tab**: Set expected seats for auto-hold optimization  
- **Held Seats Tab**: View and manage currently held seats
- **Ticket Type Selection Tab**: Configure auto-hold settings

### 2. Auto-Hold System (`auto_hold.py`)
- **TokenPool**: Manages hold token generation and caching
- **ConnectionPool**: Maintains persistent HTTP connections
- **PerformanceTracker**: Monitors response times and success rates
- **AutoHoldSystem**: Coordinates all components

### 3. Token Management
- **Account Token Manager** (`account_token_manager.py`): EliteSoftworks tokens
- **Token Retrieval** (`token_retrieval.py`): Webook hold tokens
- **Chart Token Manager** (`chart_token_manager.py`): Chart access tokens

### 4. Helper Functions (`helper.py`)
- Seat holding logic
- API request handling
- Response processing

## Key Features

### Manual Hold
- User clicks on seat in the booking tab
- System immediately attempts to hold the seat
- Uses pre-cached hold tokens for speed
- Shows success/failure feedback

### Auto Hold
- Monitors websocket for seat availability
- Automatically holds seats when they become free
- Uses preparation tab settings for optimization
- Concurrent holding for multiple seats

### Preparation System
- Pre-caches hold tokens based on expected seat count
- Maintains token pool for instant availability
- Background token generation
- Real-time status monitoring

## Performance Targets

- **Auto-hold Response**: <100ms from websocket to hold submission
- **Token Availability**: Tokens ready within 100ms of target change
- **Manual Hold**: <200ms from click to hold completion
- **Concurrent Holding**: Support for multiple simultaneous holds

## File Structure

```
webook_book_pro/
├── main.py                     # Application entry point
├── main_window.py              # Main UI window
├── auto_hold.py                # Core auto-hold system
├── helper.py                   # Seat holding logic
├── token_retrieval.py          # Hold token generation
├── account_token_manager.py    # Account token management
├── chart_token_manager.py      # Chart token management
├── webook_client.py            # Webook API client
├── tabs/
│   ├── preparation_tab.py      # Auto-hold preparation
│   ├── held_seats_tab.py       # Held seats management
│   └── ticket_type_selection_tab.py  # Auto-hold configuration
└── SYSTEM_ARCHITECTURE.md     # This file
```

## Error Handling

### Common Issues
1. **"Insufficient tokens"** - Event not loaded or token generation failed
2. **"Futures after shutdown"** - Thread pool cleanup issue (fixed)
3. **"No event ID"** - Need to load event first in booking tab

### Solutions
1. Always load event in booking tab before using auto-hold
2. Use preparation tab to pre-generate tokens
3. Check logs for detailed error information

## Usage Flow

1. **Start Application**: `python main.py`
2. **Load Event**: Use booking tab to load event data
3. **Set Preparation**: Use preparation tab to set expected seats (optional)
4. **Manual Hold**: Click seats in booking tab to hold manually
5. **Auto Hold**: Enable in ticket type selection tab for automatic holding

## System State

### Current Status
✅ **Core functionality working**: Manual hold and auto-hold
✅ **Token flow implemented**: EliteSoftworks → Webook → SeatsIO  
✅ **Clean architecture**: Removed duplicate files and systems
✅ **Error handling**: Fixed thread pool and token generation issues
✅ **UI simplified**: Focused on essential tabs only

### Removed Components
- Duplicate auto-hold systems (fast_hold.py, ultra_fast_hold_manager.py, etc.)
- Unnecessary test files (35+ files removed)
- Complex hold tab (replaced with simple booking tab functionality)
- Performance testing systems (consolidated into auto_hold.py)

The system is now clean, focused, and ready for production use.
