#!/usr/bin/env python3
"""
Request Diagnostics Tool
Tests all request types to identify what's going wrong with transfers and token generation
"""

import asyncio
import json
import time
import logging
from typing import Optional, Dict, Any
import httpx

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RequestDiagnostics:
    """Comprehensive request testing for debugging transfer failures"""
    
    def __init__(self, proxy: Optional[str] = None):
        self.proxy = proxy
        self.results = {}
        
        # Test configuration
        self.seatsio_ip = "*************"
        self.token_endpoint = f'https://{self.seatsio_ip}/chart.js'
        self.hold_endpoint = f'https://{self.seatsio_ip}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
        self.release_endpoint = f'https://{self.seatsio_ip}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-objects'
        
        # Test data (you may need to update these)
        self.test_event_key = "al-fateh-vs-al-fayha-spl-25-26-m-w-1"  # Update with actual event
        self.test_seat_id = "D7-P-4"  # One of the failing seats
        self.test_account_token = None  # Will be set from account tokens
        
    async def get_http_client(self) -> httpx.AsyncClient:
        """Create HTTP client with proxy support"""
        client_kwargs = {
            'http2': True,
            'timeout': httpx.Timeout(30.0),
            'verify': False,
            'limits': httpx.Limits(max_connections=100, max_keepalive_connections=20)
        }
        
        if self.proxy:
            parts = self.proxy.split(':')
            if len(parts) == 4:
                proxy_url = f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"
                client_kwargs['proxy'] = proxy_url
                logger.info(f"🔗 Using proxy: {parts[0]}:{parts[1]} (User: {parts[2]})")
            else:
                logger.warning(f"⚠️ Invalid proxy format: {self.proxy}")
        
        return httpx.AsyncClient(**client_kwargs)

    async def test_token_generation(self) -> Dict[str, Any]:
        """Test token generation endpoint"""
        logger.info("🧪 Testing token generation...")
        
        result = {
            'test': 'token_generation',
            'success': False,
            'status_code': None,
            'response_body': None,
            'error': None,
            'timing': None
        }
        
        try:
            # Get account token first
            from account_token_manager import get_account_tokens
            account_tokens = await get_account_tokens()
            
            if not account_tokens:
                result['error'] = "No account tokens available"
                return result
                
            self.test_account_token = account_tokens[0]
            logger.info(f"✅ Using account token: {self.test_account_token[:20]}...")
            
            # Test token generation request
            headers = {
                'Host': 'cdn-eu.seatsio.net',
                'accept': 'application/json',
                'authorization': f'Bearer {self.test_account_token}',
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            payload = {
                'chartKey': '06744f62-aa66-4c18-845a-b3baddf41402',  # Update if needed
                'event': self.test_event_key,
                'expiry': 15
            }
            
            start_time = time.perf_counter()
            
            async with await self.get_http_client() as client:
                response = await client.post(
                    self.token_endpoint,
                    json=payload,
                    headers=headers
                )
                
            end_time = time.perf_counter()
            
            result['status_code'] = response.status_code
            result['response_body'] = response.text
            result['timing'] = f"{(end_time - start_time) * 1000:.2f}ms"
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'holdToken' in data:
                        result['success'] = True
                        result['hold_token'] = data['holdToken']
                        logger.info(f"✅ Token generated successfully: {data['holdToken'][:8]}...")
                    else:
                        result['error'] = f"No holdToken in response: {data}"
                except Exception as e:
                    result['error'] = f"Failed to parse JSON: {e}"
            else:
                result['error'] = f"HTTP {response.status_code}: {response.text[:200]}"
                
        except Exception as e:
            result['error'] = f"Exception: {str(e)}"
            
        return result

    async def test_seat_hold(self, hold_token: str) -> Dict[str, Any]:
        """Test seat holding with generated token"""
        logger.info(f"🧪 Testing seat hold for {self.test_seat_id}...")
        
        result = {
            'test': 'seat_hold',
            'success': False,
            'status_code': None,
            'response_body': None,
            'error': None,
            'timing': None
        }
        
        try:
            headers = {
                'Host': 'cdn-eu.seatsio.net',
                'accept': '*/*',
                'content-type': 'application/json',
                'origin': 'https://cdn-eu.seatsio.net',
                'x-client-tool': 'Renderer',
                'x-browser-id': 'test-browser-id'
            }
            
            payload = {
                'events': [self.test_event_key],
                'holdToken': hold_token,
                'objects': [{'objectId': self.test_seat_id}],
                'channelKeys': ['NO_CHANNEL'],
                'validateEventsLinkedToSameChart': True
            }
            
            # Generate x-signature
            body_str = json.dumps(payload, separators=(',', ':'))
            from chart_token_manager import generate_x_signature
            headers['x-signature'] = generate_x_signature(body_str)
            
            start_time = time.perf_counter()
            
            async with await self.get_http_client() as client:
                response = await client.post(
                    self.hold_endpoint,
                    content=body_str,
                    headers=headers
                )
                
            end_time = time.perf_counter()
            
            result['status_code'] = response.status_code
            result['response_body'] = response.text
            result['timing'] = f"{(end_time - start_time) * 1000:.2f}ms"
            result['request_body'] = body_str
            result['request_headers'] = headers
            
            if response.status_code == 204:
                result['success'] = True
                logger.info(f"✅ Seat {self.test_seat_id} held successfully")
            else:
                result['error'] = f"HTTP {response.status_code}: {response.text[:200]}"
                logger.error(f"❌ Failed to hold seat: {result['error']}")
                
        except Exception as e:
            result['error'] = f"Exception: {str(e)}"
            
        return result

    async def test_seat_release(self, hold_token: str) -> Dict[str, Any]:
        """Test seat release"""
        logger.info(f"🧪 Testing seat release for {self.test_seat_id}...")
        
        result = {
            'test': 'seat_release',
            'success': False,
            'status_code': None,
            'response_body': None,
            'error': None,
            'timing': None
        }
        
        try:
            headers = {
                'Host': 'cdn-eu.seatsio.net',
                'accept': '*/*',
                'content-type': 'application/json',
                'origin': 'https://cdn-eu.seatsio.net',
                'x-client-tool': 'Renderer',
                'x-browser-id': 'test-browser-id'
            }
            
            payload = {
                'events': [self.test_event_key],
                'holdToken': hold_token,
                'objects': [{'objectId': self.test_seat_id}],
                'channelKeys': ['NO_CHANNEL'],
                'validateEventsLinkedToSameChart': True
            }
            
            # Generate x-signature
            body_str = json.dumps(payload, separators=(',', ':'))
            from chart_token_manager import generate_x_signature
            headers['x-signature'] = generate_x_signature(body_str)
            
            start_time = time.perf_counter()
            
            async with await self.get_http_client() as client:
                response = await client.post(
                    self.release_endpoint,
                    content=body_str,
                    headers=headers
                )
                
            end_time = time.perf_counter()
            
            result['status_code'] = response.status_code
            result['response_body'] = response.text
            result['timing'] = f"{(end_time - start_time) * 1000:.2f}ms"
            result['request_body'] = body_str
            result['request_headers'] = headers
            
            if response.status_code == 204:
                result['success'] = True
                logger.info(f"✅ Seat {self.test_seat_id} released successfully")
            else:
                result['error'] = f"HTTP {response.status_code}: {response.text[:200]}"
                logger.error(f"❌ Failed to release seat: {result['error']}")
                
        except Exception as e:
            result['error'] = f"Exception: {str(e)}"
            
        return result

    async def test_transfer_sequence(self) -> Dict[str, Any]:
        """Test complete transfer sequence: generate token → hold → release → hold with new token"""
        logger.info("🧪 Testing complete transfer sequence...")
        
        sequence_result = {
            'test': 'transfer_sequence',
            'success': False,
            'steps': [],
            'error': None
        }
        
        try:
            # Step 1: Generate first token
            logger.info("Step 1: Generate first token")
            token1_result = await self.test_token_generation()
            sequence_result['steps'].append(token1_result)
            
            if not token1_result['success']:
                sequence_result['error'] = "Failed to generate first token"
                return sequence_result
                
            token1 = token1_result['hold_token']
            
            # Step 2: Hold seat with first token
            logger.info("Step 2: Hold seat with first token")
            hold1_result = await self.test_seat_hold(token1)
            sequence_result['steps'].append(hold1_result)
            
            if not hold1_result['success']:
                sequence_result['error'] = "Failed to hold seat with first token"
                return sequence_result
            
            # Step 3: Generate second token
            logger.info("Step 3: Generate second token")
            token2_result = await self.test_token_generation()
            sequence_result['steps'].append(token2_result)
            
            if not token2_result['success']:
                sequence_result['error'] = "Failed to generate second token"
                return sequence_result
                
            token2 = token2_result['hold_token']
            
            # Step 4: Release seat from first token
            logger.info("Step 4: Release seat from first token")
            release_result = await self.test_seat_release(token1)
            sequence_result['steps'].append(release_result)
            
            # Step 5: Hold seat with second token
            logger.info("Step 5: Hold seat with second token")
            hold2_result = await self.test_seat_hold(token2)
            sequence_result['steps'].append(hold2_result)
            
            # Evaluate overall success
            if hold2_result['success']:
                sequence_result['success'] = True
                logger.info("✅ Transfer sequence completed successfully")
            else:
                sequence_result['error'] = "Failed to hold seat with second token"
                
        except Exception as e:
            sequence_result['error'] = f"Exception in transfer sequence: {str(e)}"
            
        return sequence_result

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all diagnostic tests"""
        logger.info("🚀 Starting comprehensive request diagnostics...")
        logger.info("=" * 60)
        
        all_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'proxy_used': self.proxy is not None,
            'proxy_config': self.proxy if self.proxy else None,
            'tests': {}
        }
        
        # Test 1: Token generation
        all_results['tests']['token_generation'] = await self.test_token_generation()
        
        # Test 2: Transfer sequence (if token generation works)
        if all_results['tests']['token_generation']['success']:
            all_results['tests']['transfer_sequence'] = await self.test_transfer_sequence()
        else:
            logger.error("❌ Skipping transfer tests - token generation failed")
        
        # Print summary
        self.print_summary(all_results)
        
        return all_results

    def print_summary(self, results: Dict[str, Any]):
        """Print diagnostic summary"""
        logger.info("=" * 60)
        logger.info("🎯 DIAGNOSTIC SUMMARY")
        logger.info("=" * 60)
        
        logger.info(f"📅 Timestamp: {results['timestamp']}")
        logger.info(f"🔗 Proxy Used: {'YES' if results['proxy_used'] else 'NO'}")
        if results['proxy_config']:
            parts = results['proxy_config'].split(':')
            logger.info(f"🌐 Proxy: {parts[0]}:{parts[1]} (User: {parts[2]})")
        
        logger.info("\n📊 TEST RESULTS:")
        
        for test_name, test_result in results['tests'].items():
            status = "✅ PASS" if test_result.get('success', False) else "❌ FAIL"
            logger.info(f"   {test_name}: {status}")
            
            if not test_result.get('success', False) and test_result.get('error'):
                logger.info(f"      Error: {test_result['error']}")
                
            if test_result.get('timing'):
                logger.info(f"      Timing: {test_result['timing']}")
                
            if test_result.get('status_code'):
                logger.info(f"      Status: {test_result['status_code']}")
        
        logger.info("=" * 60)

async def main():
    """Run diagnostics"""
    # Get proxy from system if available
    proxy = None
    try:
        from proxy_manager import get_proxy_string
        proxy = get_proxy_string()
        if proxy:
            logger.info(f"🔗 Using system proxy: {proxy.split(':')[0]}:{proxy.split(':')[1]}")
    except:
        logger.info("🔗 No proxy configuration found")
    
    # Run diagnostics
    diagnostics = RequestDiagnostics(proxy=proxy)
    results = await diagnostics.run_all_tests()
    
    # Save results to file
    with open('diagnostic_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("💾 Results saved to diagnostic_results.json")

if __name__ == "__main__":
    asyncio.run(main())
