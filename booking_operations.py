# booking_operations.py - Booking operations for main window
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import QMessageBox, QInputDialog
from PyQt5.QtCore import QTimer
import logging

logger = logging.getLogger("webook_pro")


class BookingOperations:
    """Handles all booking-related operations for the main window"""
    
    def __init__(self, main_window):
        self.main_window = main_window
    
    def handle_book_seats(self):
        """Handle book seats button click with fast concurrent booking"""
        if not self.main_window.token_system:
            self.main_window.log("❌ Token system not initialized. Please load an event first.")
            return

        # Get total seats to book
        try:
            total_seats = int(self.main_window.total_tickets_edit.text())
            if total_seats <= 0:
                raise ValueError("Must be positive")
        except ValueError:
            QMessageBox.warning(self.main_window, "Warning", "Please enter a valid number of seats to book.")
            return

        # Get available seats from selected ticket types
        available_seats = self.main_window.ticket_type_tab.get_available_seats_from_selection(max_seats=total_seats)
        if not available_seats:
            QMessageBox.warning(self.main_window, "Warning", "No available seats found in selected ticket types. Please select ticket types in the Ticket Type Selection tab.")
            return

        # Check if we have enough seats
        if len(available_seats) < total_seats:
            QMessageBox.warning(self.main_window, "Warning", f"Only {len(available_seats)} seats available in selected types, but requested {total_seats} seats.")
            return

        # Take only the required quantity
        seats_to_book = available_seats[:total_seats]

        self.main_window.log(f"🚀 Starting concurrent booking of {len(seats_to_book)} seats...")

        # Change button to show progress
        self.main_window.book_button.setText("Holding Seats...")
        self.main_window.book_button.setEnabled(False)

        # Use the new BATCH HOLDING system for maximum speed
        self._start_batch_booking(seats_to_book)

    def handle_release_seats(self):
        """Handle release seats button click"""
        if self.main_window.token_system:
            # Get all seats
            all_seats = self.main_window.token_system.get_all_seats()
            if not all_seats:
                self.main_window.log("[Main] No seats to release.")
                return

            # Confirm with user
            msg = QMessageBox.question(
                self.main_window,
                "Confirm Release",
                f"Are you sure you want to release all {len(all_seats)} seats?",
                QMessageBox.Yes | QMessageBox.No
            )

            if msg != QMessageBox.Yes:
                return

            # Release all seats
            released_count = 0
            for seat_id in list(all_seats.keys()):
                if self.main_window.token_system.release_seat(seat_id):
                    released_count += 1

            self.main_window.log(f"Released {released_count}/{len(all_seats)} seats")
            self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
            return

        # No token system initialized
        self.main_window.log("[Main] Token system not initialized. Cannot release seats.")

    def _start_batch_booking(self, seat_ids):
        """NEW BATCH BOOKING - Uses auto-hold system for true concurrent batch holding"""
        try:
            self._batch_start_time = time.perf_counter()
            total_seats = len(seat_ids)

            self.main_window.log(f"🚀 BATCH BOOKING: Starting batch hold of {total_seats} seats...")

            # Check if auto-hold system is available
            if not hasattr(self.main_window, 'auto_hold_system') or not self.main_window.auto_hold_system:
                self.main_window.log(f"⚠️ Auto-hold system not available, falling back to old method")
                self._start_concurrent_booking(seat_ids)
                return

            # Initialize batch tracking
            self.main_window._batch_booking_results = {'success': 0, 'failed': 0, 'total': total_seats}

            # Calculate expected performance (high concurrency with multiple requests per token)
            # Each token can handle multiple concurrent requests up to 50 seats total
            expected_concurrent_calls = min(total_seats, total_seats // 5)  # Estimate based on small batches
            self.main_window.log(f"🎯 BATCH BOOKING: {total_seats} seats → ~{expected_concurrent_calls} concurrent API calls")

            def run_batch_booking():
                """Run batch booking with intelligent retries"""
                try:
                    remaining_seats = seat_ids.copy()
                    attempt = 1
                    max_attempts = 3
                    total_held = 0

                    while attempt <= max_attempts and remaining_seats:
                        QTimer.singleShot(0, lambda a=attempt, r=len(remaining_seats): self.main_window.log(f"🔄 ATTEMPT {a}/{max_attempts}: Trying to hold {r} seats..."))

                        # Reset batch results for this attempt
                        self.main_window._batch_booking_results = {'success': 0, 'failed': 0, 'total': len(remaining_seats)}

                        # Submit batch hold
                        batch_start = time.perf_counter()
                        success = self.main_window.auto_hold_system.hold_multiple_seats(remaining_seats)
                        batch_time = (time.perf_counter() - batch_start) * 1000

                        if success:
                            QTimer.singleShot(0, lambda a=attempt, t=batch_time: self.main_window.log(f"⚡ ATTEMPT {a}: Submitted in {t:.1f}ms"))

                            # Wait for operations to complete with timeout
                            wait_time = max(3, min(10, len(remaining_seats) // 50))  # 3-10 seconds based on seat count
                            time.sleep(wait_time)

                            # Count successful holds
                            attempt_success = self.main_window._batch_booking_results.get('success', 0)
                            total_held += attempt_success

                            QTimer.singleShot(0, lambda a=attempt, s=attempt_success: self.main_window.log(f"✅ ATTEMPT {a}: {s} seats held successfully"))

                            if attempt < max_attempts and attempt_success < len(remaining_seats):
                                # Get fresh available seats for retry
                                QTimer.singleShot(0, lambda: self.main_window.log(f"🔄 Getting fresh seats for retry..."))
                                fresh_seats = self._get_fresh_available_seats(len(remaining_seats) - attempt_success)
                                remaining_seats = fresh_seats
                                QTimer.singleShot(0, lambda f=len(fresh_seats): self.main_window.log(f"🎯 Found {f} fresh seats for next attempt"))
                            else:
                                remaining_seats = []
                        else:
                            QTimer.singleShot(0, lambda a=attempt: self.main_window.log(f"❌ ATTEMPT {a}: Failed to submit batch"))
                            break

                        attempt += 1

                    # Final results
                    total_time = (time.perf_counter() - self._batch_start_time) * 1000
                    QTimer.singleShot(0, lambda: self._on_batch_booking_complete_with_results(total_held, total_seats, total_time))

                except Exception as e:
                    QTimer.singleShot(0, lambda: self.main_window.log(f"❌ BATCH BOOKING ERROR: {str(e)}"))
                    # Ensure button is restored even on error
                    QTimer.singleShot(0, lambda: self._restore_booking_button())
                    QTimer.singleShot(0, lambda: self._start_concurrent_booking(seat_ids))

            # Start batch booking in background thread
            batch_thread = threading.Thread(target=run_batch_booking, daemon=True)
            batch_thread.start()

            # Safety timeout to restore button if something goes wrong
            def safety_timeout():
                if hasattr(self.main_window, 'book_button') and self.main_window.book_button.text() != "Hold Seats from Selected Types (Fast)":
                    self.main_window.log("⚠️ Safety timeout triggered - restoring button")
                    self._restore_booking_button()

            QTimer.singleShot(60000, safety_timeout)  # 60 second safety timeout

        except Exception as e:
            self.main_window.log(f"❌ Failed to start batch booking: {str(e)}")
            self.main_window.log("🔄 Falling back to concurrent booking...")
            self._start_concurrent_booking(seat_ids)

    def _start_concurrent_booking(self, seat_ids):
        """Start fast concurrent booking of selected seats with crash protection"""
        try:
            start_time = time.perf_counter()
            self.main_window.log(f"⚡ Starting concurrent booking of {len(seat_ids)} seats...")
            self.main_window.log(f"📝 Note: Seats will be held with system tokens. Use transfer later if needed.")

            # Limit concurrent operations to prevent crashes
            max_concurrent = min(20, len(seat_ids))  # Reduced from 50 to 20 for stability
            self.main_window.log(f"🔧 Using {max_concurrent} concurrent workers for stability")

            # Track booking results
            self.booking_results = {
                'total': len(seat_ids),
                'success': 0,
                'failed': 0,
                'completed': 0
            }

            def book_single_seat(seat_id):
                """Hold a single seat using system tokens - runs in thread pool"""
                try:
                    # Validate inputs
                    if not seat_id or not isinstance(seat_id, str):
                        return {'seat_id': seat_id, 'success': False, 'error': 'Invalid seat ID'}

                    # Hold the seat using our token system
                    if not self.main_window.token_system:
                        return {'seat_id': seat_id, 'success': False, 'error': 'No token system available'}

                    try:
                        hold_success = self.main_window.token_system.hold_seat(seat_id)
                        if hold_success:
                            return {'seat_id': seat_id, 'success': True, 'error': None}
                        else:
                            return {'seat_id': seat_id, 'success': False, 'error': 'Failed to hold seat'}
                    except Exception as hold_error:
                        return {'seat_id': seat_id, 'success': False, 'error': f'Hold error: {str(hold_error)}'}

                except Exception as e:
                    return {'seat_id': seat_id, 'success': False, 'error': f'Unexpected error: {str(e)}'}

            def on_booking_complete():
                """Called when all booking operations complete"""
                total_time = (time.perf_counter() - start_time) * 1000
                success_count = self.booking_results['success']
                failed_count = self.booking_results['failed']

                self.main_window.log(f"✅ Seat holding completed in {total_time:.1f}ms")
                self.main_window.log(f"📊 Results: {success_count} seats held, {failed_count} failed")
                if success_count > 0:
                    self.main_window.log(f"💡 Tip: Use the transfer function later if you need to move seats to your personal token")

                # Restore button
                self.main_window.book_button.setText("Hold Seats from Selected Types (Fast)")
                self.main_window.book_button.setEnabled(True)

                # Refresh UI
                self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

            def run_concurrent_booking():
                """Run the concurrent booking in a background thread"""
                try:
                    # Use ThreadPoolExecutor with limited concurrency for stability
                    with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
                        # Submit all booking tasks
                        future_to_seat = {executor.submit(book_single_seat, seat_id): seat_id
                                        for seat_id in seat_ids}

                        # Process results as they complete
                        for future in as_completed(future_to_seat):
                            try:
                                result = future.result(timeout=30)  # 30 second timeout per seat

                                if result['success']:
                                    self.booking_results['success'] += 1
                                else:
                                    self.booking_results['failed'] += 1
                                    QTimer.singleShot(0, lambda r=result: self.main_window.log(f"❌ Failed to book {r['seat_id']}: {r['error']}"))

                                self.booking_results['completed'] += 1

                                # Update progress every 10 seats or on completion
                                if self.booking_results['completed'] % 10 == 0 or self.booking_results['completed'] == self.booking_results['total']:
                                    progress = (self.booking_results['completed'] / self.booking_results['total']) * 100
                                    QTimer.singleShot(0, lambda p=progress: self.main_window.log(f"📈 Progress: {p:.0f}%"))

                            except Exception as future_error:
                                # Handle individual future errors
                                self.booking_results['failed'] += 1
                                self.booking_results['completed'] += 1
                                QTimer.singleShot(0, lambda: self.main_window.log(f"❌ Booking task error: {str(future_error)}"))

                    # Schedule completion callback on main thread
                    QTimer.singleShot(0, on_booking_complete)

                except Exception as e:
                    # Handle any errors in the booking process
                    QTimer.singleShot(0, lambda: self.main_window.log(f"❌ Booking error: {str(e)}"))
                    QTimer.singleShot(0, lambda: self.main_window.book_button.setText("Hold Seats from Selected Types (Fast)"))
                    QTimer.singleShot(0, lambda: self.main_window.book_button.setEnabled(True))

            # Start booking in background thread
            booking_thread = threading.Thread(target=run_concurrent_booking, daemon=True)
            booking_thread.start()

        except Exception as e:
            # Handle any setup errors
            self.main_window.log(f"❌ Failed to start concurrent booking: {str(e)}")
            self.main_window.log("🔄 Falling back to sequential booking...")
            self._start_sequential_booking(seat_ids)

    def _start_sequential_booking(self, seat_ids):
        """Fallback sequential booking method"""
        try:
            start_time = time.perf_counter()
            self.main_window.log(f"🐌 Starting sequential seat holding of {len(seat_ids)} seats...")

            def sequential_booking():
                success_count = 0
                failed_count = 0

                # Hold seats one by one (no user token needed)
                for i, seat_id in enumerate(seat_ids):
                    try:
                        if self.main_window.token_system:
                            # Hold the seat using system tokens
                            hold_success = self.main_window.token_system.hold_seat(seat_id)
                            if hold_success:
                                success_count += 1
                            else:
                                failed_count += 1
                        else:
                            failed_count += 1

                        # Update progress every 10 seats
                        if (i + 1) % 10 == 0 or (i + 1) == len(seat_ids):
                            progress = ((i + 1) / len(seat_ids)) * 100
                            QTimer.singleShot(0, lambda p=progress: self.main_window.log(f"📈 Sequential Progress: {p:.0f}%"))

                    except Exception as seat_error:
                        failed_count += 1
                        QTimer.singleShot(0, lambda e=seat_error: self.main_window.log(f"❌ Seat booking error: {str(e)}"))

                # Completion
                total_time = (time.perf_counter() - start_time) * 1000
                QTimer.singleShot(0, lambda: self.main_window.log(f"✅ Sequential seat holding completed in {total_time:.1f}ms"))
                QTimer.singleShot(0, lambda: self.main_window.log(f"📊 Results: {success_count} seats held, {failed_count} failed"))
                if success_count > 0:
                    QTimer.singleShot(0, lambda: self.main_window.log(f"💡 Tip: Use the transfer function later if you need to move seats to your personal token"))
                QTimer.singleShot(0, lambda: self.main_window.book_button.setText("Hold Seats from Selected Types (Fast)"))
                QTimer.singleShot(0, lambda: self.main_window.book_button.setEnabled(True))

            # Start sequential booking in background thread
            booking_thread = threading.Thread(target=sequential_booking, daemon=True)
            booking_thread.start()

        except Exception as e:
            self.main_window.log(f"❌ Sequential booking also failed: {str(e)}")
            self.main_window.book_button.setText("Hold Seats from Selected Types (Fast)")
            self.main_window.book_button.setEnabled(True)

    def _restore_booking_button(self):
        """Restore the booking button to its normal state"""
        try:
            self.main_window.book_button.setText("Hold Seats from Selected Types (Fast)")
            self.main_window.book_button.setEnabled(True)
        except Exception as e:
            self.main_window.log(f"⚠️ Error restoring button: {str(e)}")

    def _get_fresh_available_seats(self, count: int):
        """Get fresh available seats for retry attempts"""
        try:
            # Get currently selected ticket types
            selected_types = self.main_window.ticket_type_tab.get_selected_types()
            if not selected_types:
                return []

            # Get available seats from selected types
            available_seats = []
            for ticket_type, seats in self.main_window.tickets_info.items():
                if ticket_type in selected_types:
                    for seat_id, seat_info in seats.items():
                        if seat_info.get('status') == 'free':
                            available_seats.append(seat_id)

            # Return requested count
            return available_seats[:count]
        except Exception as e:
            self.main_window.log(f"⚠️ Error getting fresh seats: {str(e)}")
            return []

    def _on_batch_booking_complete_with_results(self, total_held: int, total_requested: int, total_time_ms: float):
        """Handle completion of batch booking with detailed results"""
        try:
            success_rate = (total_held / total_requested * 100) if total_requested > 0 else 0

            self.main_window.log(f"🎯 BATCH BOOKING COMPLETE:")
            self.main_window.log(f"   ✅ Successfully held: {total_held}/{total_requested} seats ({success_rate:.1f}%)")
            self.main_window.log(f"   ⏱️ Total time: {total_time_ms:.0f}ms ({total_time_ms/1000:.1f}s)")

            if total_held > 0:
                avg_time_per_seat = total_time_ms / total_held
                self.main_window.log(f"   📊 Average: {avg_time_per_seat:.1f}ms per seat")

            # Restore button
            self._restore_booking_button()

            # Delay UI refresh to show all held seats
            QTimer.singleShot(1000, lambda: self._delayed_batch_ui_refresh())

        except Exception as e:
            self.main_window.log(f"❌ Error in batch booking completion: {str(e)}")

    def _delayed_batch_ui_refresh(self):
        """Delayed UI refresh after batch booking to avoid threading conflicts"""
        try:
            # Refresh UI to show held seats
            self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
            self.main_window._update_window_title()
            self.main_window.log(f"🔄 UI refreshed - check Held Seats tab for results")
        except Exception as e:
            self.main_window.log(f"⚠️ Error refreshing UI after batch booking: {str(e)}")
            # Try again with a longer delay if first attempt fails
            QTimer.singleShot(2000, lambda: self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True))
