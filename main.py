# main.py
# Import warning suppression first
import suppress_warnings  # noqa: F401

import sys
import os
import warnings
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import qInstallMessageHandler, QtMsgType
from logger_setup import setup_logger
from elitesoftworks import print_logo
from main_window import MainWindow
from script_info import SCRIPT_NAME, VERSION
import logging
import traceback

DEBUG = False
logging.basicConfig(level=logging.INFO)
logging.getLogger().setLevel(logging.INFO)

def qt_message_handler(mode, context, message):
    """Custom Qt message handler to suppress specific warnings"""
    # Suppress Qt meta type warnings
    if "Cannot queue arguments of type" in message and "qRegisterMetaType" in message:
        return
    # Suppress other Qt warnings we don't care about
    if "QVector<int>" in message or "QTextCursor" in message:
        return
    # Let other messages through (you can comment this out to suppress all Qt messages)
    # print(f"Qt {mode}: {message}")

def main():
    try:
        # Suppress Qt meta type warnings
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.xcb.warning=false'

        # Suppress Python warnings for event loop issues
        warnings.filterwarnings("ignore", message=".*Event loop is closed.*")

        # Initialize the logger
        logger = setup_logger()
        if DEBUG:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)
        print_logo(script_name=SCRIPT_NAME, version=VERSION)

        app = QApplication(sys.argv)
        app.setOrganizationName("Elitesoftworks")
        app.setApplicationName("webookBookingPro")

        # Install custom Qt message handler to suppress warnings
        qInstallMessageHandler(qt_message_handler)

        window = MainWindow()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"An error occurred: {e}")
        traceback.print_exc()
        logger.critical(f"An error occurred: {e}")
        logger.critical(traceback.format_exc())
        sys.exit(1)



if __name__ == "__main__":
    main()
