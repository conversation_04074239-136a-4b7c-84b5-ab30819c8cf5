# ui_components.py - UI creation and management for main window
from PyQt5.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QTextEdit, 
    QPushButton, QComboBox, QTabWidget, QWidget
)
from PyQt5.QtCore import Qt


class UIComponents:
    """Handles UI component creation and management for the main window"""
    
    def __init__(self, main_window):
        self.main_window = main_window
    
    def create_booking_tab_ui(self):
        """Create the main booking tab UI"""
        layout = QVBoxLayout()

        # Create form fields
        self.main_window.auth_name_edit = QLineEdit()
        self.main_window.auth_name_edit.setPlaceholderText("Enter your username...")

        self.main_window.hwid_check_button = QPushButton("Check username")

        self.main_window.event_key_edit = QLineEdit()
        self.main_window.event_key_edit.setPlaceholderText("Enter event key...")

        self.main_window.total_tickets_edit = QLineEdit()
        self.main_window.total_tickets_edit.setPlaceholderText("Enter total seats to book...")

        # Connect to preparation system - only prepare when user finishes typing
        self.main_window.total_tickets_edit.editingFinished.connect(
            self.main_window._on_tickets_editing_finished
        )

        self.main_window.team_combo = QComboBox()

        self.main_window.log_text = QTextEdit()
        self.main_window.log_text.setReadOnly(True)

        # Create buttons
        self.main_window.load_button = QPushButton("Load Event Info")
        self.main_window.book_button = QPushButton("Hold Seats from Selected Types (Fast)")
        self.main_window.release_button = QPushButton("Release All Seats")
        self.main_window.test_auto_hold_button = QPushButton("Test Auto Hold")
        self.main_window.about_button = QPushButton("About")
        self.main_window.configure_proxy_button = QPushButton("Configure Proxy")
        self.main_window.performance_button = QPushButton("Performance Monitor")

        # Connect performance button
        self.main_window.performance_button.clicked.connect(
            self.main_window.show_performance_monitor
        )

        # Create form layout
        form_layout = QFormLayout()
        form_layout.addRow("Auth Name:", self.main_window.auth_name_edit)
        form_layout.addRow("", self.main_window.hwid_check_button)
        form_layout.addRow("Event Key:", self.main_window.event_key_edit)
        form_layout.addRow("Total Seats:", self.main_window.total_tickets_edit)
        form_layout.addRow("Choose Team:", self.main_window.team_combo)
        form_layout.addRow(self.main_window.configure_proxy_button)
        form_layout.addRow(self.main_window.performance_button)

        # Create button layout
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.main_window.load_button)
        button_layout.addWidget(self.main_window.book_button)
        button_layout.addWidget(self.main_window.release_button)
        button_layout.addWidget(self.main_window.test_auto_hold_button)
        button_layout.addWidget(self.main_window.about_button)

        # Add layouts to main layout
        layout.addLayout(form_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.main_window.log_text)

        self.main_window.booking_tab.setLayout(layout)

        # Initial state - disable until HWID check
        self.set_fields_enabled(False)
        self.main_window.about_button.setEnabled(True)

    def set_fields_enabled(self, enabled: bool):
        """Enable or disable form fields"""
        self.main_window.event_key_edit.setEnabled(enabled)
        self.main_window.total_tickets_edit.setEnabled(enabled)
        self.main_window.team_combo.setEnabled(enabled)
        self.main_window.load_button.setEnabled(enabled)
        self.main_window.book_button.setEnabled(enabled)
        self.main_window.release_button.setEnabled(enabled)
        self.main_window.test_auto_hold_button.setEnabled(enabled)

    def setup_main_tabs(self):
        """Setup the main tab widget and all tabs"""
        # Main tab widget
        self.main_window.tab_widget = QTabWidget()
        self.main_window.setCentralWidget(self.main_window.tab_widget)

        # Booking tab
        self.main_window.booking_tab = QWidget()
        self.create_booking_tab_ui()
        self.main_window.tab_widget.addTab(self.main_window.booking_tab, "Booking")

        # Preparation tab
        from tabs.preparation_tab import PreparationTab
        self.main_window.preparation_tab = PreparationTab(self.main_window)
        self.main_window.preparation_tab.setEnabled(False)
        self.main_window.tab_widget.addTab(self.main_window.preparation_tab, "Preparation")

        # Held seats tab
        from tabs.held_seats_tab import HeldSeatsTab
        self.main_window.held_seats_tab = HeldSeatsTab(self.main_window)
        self.main_window.held_seats_tab.setEnabled(False)
        self.main_window.tab_widget.addTab(self.main_window.held_seats_tab, "Held Seats")

        # Ticket type selection tab
        from tabs.ticket_type_selection_tab import TicketTypeSelectionTab
        self.main_window.ticket_type_tab = TicketTypeSelectionTab(self.main_window)
        self.main_window.ticket_type_tab.setEnabled(False)
        self.main_window.tab_widget.addTab(self.main_window.ticket_type_tab, "Ticket Type Selection")

    def update_initial_ui(self):
        """Initial UI update after loading event data"""
        # Ensure tabs are enabled
        self.main_window.preparation_tab.setEnabled(True)
        self.main_window.ticket_type_tab.setEnabled(True)
        self.main_window.held_seats_tab.setEnabled(True)

        # Update with seat data
        self.main_window.ticket_type_tab.update_tickets_info(self.main_window.tickets_info)
        self.main_window.held_seats_tab.auto_refresh_held_seats()

    def enable_tabs_after_hwid_check(self):
        """Enable tabs after successful HWID check"""
        self.main_window.preparation_tab.setEnabled(True)
        self.main_window.held_seats_tab.setEnabled(True)
        self.main_window.ticket_type_tab.setEnabled(True)

    def disable_tabs_after_failed_hwid_check(self):
        """Disable tabs after failed HWID check"""
        self.main_window.preparation_tab.setEnabled(False)
        self.main_window.held_seats_tab.setEnabled(False)
        self.main_window.ticket_type_tab.setEnabled(False)
