# tabs/preparation_tab.py - Preparation tab for auto-hold optimization
import logging
import time
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, 
    QLineEdit, QPushButton, QTextEdit, QProgressBar, QGroupBox,
    QSpinBox, QCheckBox, QComboBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

logger = logging.getLogger("webook_pro")

class PreparationTab(QWidget):
    """
    Preparation tab for setting up auto-hold optimization and token pre-caching.
    This tab allows users to specify the expected number of seats they want to hold
    and pre-caches tokens for optimal performance.
    """
    
    # Signals
    seats_target_changed = pyqtSignal(int)  # Emitted when target seats changes
    preparation_status_changed = pyqtSignal(dict)  # Emitted when preparation status changes
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.auto_hold_system = None
        self.preparation_active = False
        self.target_seats = 0
        self.tokens_ready = 0
        self.tokens_needed = 0
        
        self.setup_ui()
        self.setup_timers()
        
    def setup_ui(self):
        """Set up the preparation tab UI"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("Auto-Hold Preparation")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(
            "Configure the expected number of seats for optimal auto-hold performance.\n"
            "The system will pre-cache tokens and establish connections for maximum speed."
        )
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)
        
        # Configuration Group
        config_group = QGroupBox("Configuration")
        config_layout = QFormLayout()
        
        # Target seats input
        self.seats_input = QSpinBox()
        self.seats_input.setMinimum(1)
        self.seats_input.setMaximum(1000)
        self.seats_input.setValue(50)
        self.seats_input.setSuffix(" seats")
        self.seats_input.valueChanged.connect(self.on_seats_changed)
        config_layout.addRow("Expected Seats:", self.seats_input)
        
        # Auto-preparation checkbox
        self.auto_prep_checkbox = QCheckBox("Auto-prepare tokens")
        self.auto_prep_checkbox.setChecked(True)
        self.auto_prep_checkbox.setToolTip("Automatically prepare tokens when seats target changes")
        config_layout.addRow("", self.auto_prep_checkbox)
        
        # Preparation strategy
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems([
            "Balanced (Recommended)",
            "Speed Optimized", 
            "Memory Optimized"
        ])
        config_layout.addRow("Strategy:", self.strategy_combo)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # Status Group
        status_group = QGroupBox("Preparation Status")
        status_layout = QVBoxLayout()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        # Status labels
        self.status_label = QLabel("Ready to configure")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        # Token status
        self.token_status_label = QLabel("Tokens: 0/0 ready")
        status_layout.addWidget(self.token_status_label)
        
        # Connection status
        self.connection_status_label = QLabel("Connections: Not established")
        status_layout.addWidget(self.connection_status_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.prepare_button = QPushButton("Prepare System")
        self.prepare_button.clicked.connect(self.start_preparation)
        button_layout.addWidget(self.prepare_button)
        
        self.stop_button = QPushButton("Stop Preparation")
        self.stop_button.clicked.connect(self.stop_preparation)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.reset_button = QPushButton("Reset")
        self.reset_button.clicked.connect(self.reset_preparation)
        button_layout.addWidget(self.reset_button)
        
        layout.addWidget(QWidget())  # Spacer
        layout.addLayout(button_layout)
        
        # Log area
        log_group = QGroupBox("Preparation Log")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        self.setLayout(layout)
        
    def setup_timers(self):
        """Set up update timers"""
        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second
        
    def on_seats_changed(self, value):
        """Handle seats target change"""
        self.target_seats = value
        self.calculate_tokens_needed()
        self.seats_target_changed.emit(value)
        
        if self.auto_prep_checkbox.isChecked() and hasattr(self.main_window, 'webook_data'):
            # Auto-prepare if event is loaded
            QTimer.singleShot(500, self.start_preparation)  # Small delay to avoid rapid changes
            
    def calculate_tokens_needed(self):
        """Calculate how many tokens are needed for the target seats"""
        if self.target_seats == 0:
            self.tokens_needed = 0
        else:
            seats_per_token = 50  # From auto_hold.py SEATS_PER_TOKEN
            self.tokens_needed = max(1, (self.target_seats + seats_per_token - 1) // seats_per_token)
        self.log(f"Target: {self.target_seats} seats → {self.tokens_needed} tokens needed")
        
    def start_preparation(self):
        """Start the preparation process"""
        if not hasattr(self.main_window, 'webook_data') or not self.main_window.webook_data:
            self.log("⚠️ Please load an event first")
            return

        if self.preparation_active:
            self.log("⚠️ Preparation already in progress")
            return

        # Handle 0 seats case quickly
        if self.target_seats == 0:
            self.log("⚡ No seats to prepare - system ready")
            return

        self.preparation_active = True
        self.prepare_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        self.log(f"🚀 Starting preparation for {self.target_seats} seats...")

        # Initialize auto-hold system asynchronously to avoid UI blocking
        QTimer.singleShot(0, self.initialize_auto_hold_system)
        
    def initialize_auto_hold_system(self):
        """Initialize the auto-hold system with current event data"""
        try:
            # Check if main window has auto_hold_system already
            if hasattr(self.main_window, 'auto_hold_system') and self.main_window.auto_hold_system:
                self.auto_hold_system = self.main_window.auto_hold_system
                self.log("✅ Using existing auto-hold system")
            else:
                # Initialize new auto-hold system
                from auto_hold import initialize_auto_hold

                event_data = self.main_window.webook_data['data']['seats_io']

                self.auto_hold_system = initialize_auto_hold(
                    event_key=event_data['event_key'],
                    channel_keys=self.main_window.webook_data['data']['channel_keys'],
                    team_id=getattr(self.main_window.team_combo.currentData(), 'id', None) if hasattr(self.main_window, 'team_combo') else None,
                    proxy=self.main_window.get_proxy_string() if hasattr(self.main_window, 'get_proxy_string') else None
                )

                # Set up callbacks if main window has them
                if hasattr(self.main_window, 'on_auto_hold_success') and hasattr(self.main_window, 'on_auto_hold_failure'):
                    self.auto_hold_system.set_callbacks(
                        success_callback=self.main_window.on_auto_hold_success,
                        failure_callback=self.main_window.on_auto_hold_failure
                    )

                # Store in main window for reuse
                self.main_window.auto_hold_system = self.auto_hold_system

            if self.auto_hold_system:
                self.log("🔧 Setting target seats count...")
                # Set target seats for token preparation (non-blocking)
                self.auto_hold_system.set_target_seats_count(self.target_seats)
                self.log(f"✅ Auto-hold system ready for {self.target_seats} seats")

                self.log("🔧 Starting background token generation...")
                # Start background token generation (non-blocking)
                tokens_needed = max(1, (self.target_seats + 49) // 50)  # 50 seats per token
                self.auto_hold_system.token_pool._generate_tokens_background(tokens_needed)
                self.log(f"🔑 Started generating {tokens_needed} tokens in background")

            else:
                self.log("❌ Failed to initialize auto-hold system")
                self.stop_preparation()

        except Exception as e:
            self.log(f"❌ Error initializing auto-hold system: {str(e)}")
            self.stop_preparation()
            
    def stop_preparation(self):
        """Stop the preparation process"""
        self.preparation_active = False
        self.prepare_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.log("⏹️ Preparation stopped")
        
    def reset_preparation(self):
        """Reset the preparation system"""
        self.stop_preparation()
        self.tokens_ready = 0
        self.auto_hold_system = None
        self.status_label.setText("Ready to configure")
        self.token_status_label.setText("Tokens: 0/0 ready")
        self.connection_status_label.setText("Connections: Not established")
        self.log("🔄 Preparation system reset")
        
    def update_status(self):
        """Update the status display"""
        if not self.preparation_active:
            return
            
        if self.auto_hold_system:
            try:
                # Get status from auto-hold system
                status = self.auto_hold_system.get_preparation_status()
                
                self.tokens_ready = status.get('tokens_ready', 0)
                connections_ready = status.get('connections_ready', 0)
                
                # Update progress
                if self.tokens_needed > 0:
                    progress = min(100, (self.tokens_ready / self.tokens_needed) * 100)
                    self.progress_bar.setValue(int(progress))
                
                # Update labels
                self.token_status_label.setText(f"Tokens: {self.tokens_ready}/{self.tokens_needed} ready")
                self.connection_status_label.setText(f"Connections: {connections_ready} established")
                
                if self.tokens_ready >= self.tokens_needed:
                    self.status_label.setText("✅ System ready for optimal performance")
                    if self.preparation_active:
                        self.log(f"✅ Preparation complete! {self.tokens_ready} tokens ready")
                        self.stop_preparation()
                else:
                    self.status_label.setText(f"⚡ Preparing... {self.tokens_ready}/{self.tokens_needed} tokens")
                    
            except Exception as e:
                logger.error(f"Error updating preparation status: {str(e)}")
                
    def log(self, message):
        """Add a message to the preparation log"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        logger.info(f"[Preparation] {message}")
        
    def prepare_for_seats(self, num_seats):
        """Prepare tokens for the specified number of seats"""
        if num_seats <= 0:
            return

        # Update the seats input and trigger preparation
        self.seats_input.setValue(num_seats)
        self.target_seats = num_seats
        self.calculate_tokens_needed()

        # Start preparation if auto-prep is enabled or force start
        if hasattr(self.main_window, 'webook_data') and self.main_window.webook_data:
            self.start_preparation()
        else:
            self.log("⚠️ Please load an event first")

    def get_preparation_status(self):
        """Get current preparation status"""
        return {
            'active': self.preparation_active,
            'target_seats': self.target_seats,
            'tokens_ready': self.tokens_ready,
            'tokens_needed': self.tokens_needed,
            'ready': self.tokens_ready >= self.tokens_needed and self.tokens_needed > 0
        }
