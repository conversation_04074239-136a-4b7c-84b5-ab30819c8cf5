"""
Auto Hold System - Ultra-High Performance Seat Holding
Single optimized system for sub-100ms seat holding with detailed performance benchmarking.
Supports up to 1000 concurrent seats with pre-cached tokens and minimal overhead.
"""

import asyncio
import json
import logging
import time
import threading
import secrets
import socket
import ssl
from typing import Optional, Dict, List, Any, Callable
from collections import deque, defaultdict
from concurrent.futures import ThreadPoolExecutor
import httpx

# Import existing helper functions
from helper import build_channel_keys, async_make_request
from chart_token_manager import generate_x_signature
from token_retrieval import get_hold_token, get_cached_event_id
from account_identity_manager import get_account_identity_manager

async def check_token_time_remaining(hold_token: str, proxy: Optional[str] = None) -> int:
    """
    Check how much time is left on a token (tokens are already active when generated)
    This is just for monitoring, not activation
    """
    try:
        # Use GET instead of POST since we're just checking status
        url = f'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/hold-tokens/{hold_token}'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = await async_make_request('GET', url, proxy=proxy, headers=headers)

        if response.status_code == 200:
            try:
                data = response.json()
                time_left = data.get('expiresInSeconds', TOKEN_EXPIRY_SECONDS)
                logger.debug(f"Token {hold_token[:8]}... has {time_left} seconds remaining")
                return time_left
            except Exception as e:
                logger.debug(f"Error parsing token status: {e}")
                return TOKEN_EXPIRY_SECONDS
        else:
            logger.debug(f"Token status check failed: {response.status_code}")
            return TOKEN_EXPIRY_SECONDS

    except Exception as e:
        logger.debug(f"Error checking token time: {e}")
        return TOKEN_EXPIRY_SECONDS

logger = logging.getLogger("webook_pro")

# Constants for optimization
SEATSIO_IP = "*************"
HOLD_URL = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
MAX_CONCURRENT_HOLDS = 1000  # Maximum seats we can handle
MAX_THREAD_WORKERS = 50      # Optimal thread pool size for performance
TOKEN_POOL_SIZE = 10  # Reduced default since each token can hold 50 seats
TOKEN_REFRESH_THRESHOLD = 300  # 5 minutes
SEATS_PER_TOKEN = 50  # Each token can hold up to 50 seats
TOKEN_EXPIRY_SECONDS = 600  # 10 minutes default expiry

class PerformanceTracker:
    """Detailed performance tracking for auto-hold operations"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.lock = threading.RLock()
        
        # Timing metrics (all in milliseconds)
        self.websocket_to_decision = deque(maxlen=max_samples)
        self.decision_to_request = deque(maxlen=max_samples)
        self.request_to_response = deque(maxlen=max_samples)
        self.total_response_times = deque(maxlen=max_samples)
        
        # Success/failure tracking
        self.attempts = 0
        self.successes = 0
        self.failures = 0
        self.token_failures = 0
        self.network_failures = 0
        
        # Current operation tracking
        self.active_operations = {}
        
    def start_operation(self, seat_id: str, websocket_timestamp: Optional[float] = None) -> str:
        """Start tracking a new hold operation"""
        operation_id = f"{seat_id}_{int(time.time() * 1000000)}"
        start_time = time.perf_counter()
        
        with self.lock:
            self.attempts += 1
            self.active_operations[operation_id] = {
                'seat_id': seat_id,
                'start_time': start_time,
                'websocket_timestamp': websocket_timestamp or start_time,
                'decision_time': None,
                'request_time': None,
                'response_time': None
            }
            
        return operation_id
    
    def record_decision(self, operation_id: str):
        """Record when decision to hold was made"""
        decision_time = time.perf_counter()
        
        with self.lock:
            if operation_id in self.active_operations:
                op = self.active_operations[operation_id]
                op['decision_time'] = decision_time
                
                # Calculate websocket to decision time
                ws_to_decision_ms = (decision_time - op['websocket_timestamp']) * 1000
                self.websocket_to_decision.append(ws_to_decision_ms)
    
    def record_request_start(self, operation_id: str):
        """Record when network request started"""
        request_time = time.perf_counter()
        
        with self.lock:
            if operation_id in self.active_operations:
                op = self.active_operations[operation_id]
                op['request_time'] = request_time
                
                # Calculate decision to request time
                if op['decision_time']:
                    decision_to_req_ms = (request_time - op['decision_time']) * 1000
                    self.decision_to_request.append(decision_to_req_ms)
    
    def complete_operation(self, operation_id: str, success: bool, failure_reason: str = None):
        """Complete an operation and record final metrics"""
        response_time = time.perf_counter()
        
        with self.lock:
            if operation_id not in self.active_operations:
                return
                
            op = self.active_operations[operation_id]
            op['response_time'] = response_time
            
            # Calculate final timings
            if op['request_time']:
                req_to_resp_ms = (response_time - op['request_time']) * 1000
                self.request_to_response.append(req_to_resp_ms)
            
            total_time_ms = (response_time - op['start_time']) * 1000
            self.total_response_times.append(total_time_ms)
            
            # Update counters
            if success:
                self.successes += 1
            else:
                self.failures += 1
                if failure_reason == 'token':
                    self.token_failures += 1
                elif failure_reason == 'network':
                    self.network_failures += 1
            
            # Log detailed performance info
            seat_id = op['seat_id']
            ws_to_decision = (op['decision_time'] - op['websocket_timestamp']) * 1000 if op['decision_time'] else 0
            decision_to_req = (op['request_time'] - op['decision_time']) * 1000 if op['request_time'] and op['decision_time'] else 0
            req_to_resp = req_to_resp_ms if op['request_time'] else 0
            
            status = "✅ SUCCESS" if success else f"❌ FAILED ({failure_reason})"
            logger.info(f"🎯 HOLD PERFORMANCE [{seat_id}] {status}")
            logger.info(f"   📊 WS→Decision: {ws_to_decision:.2f}ms | Decision→Request: {decision_to_req:.2f}ms | Request→Response: {req_to_resp:.2f}ms | TOTAL: {total_time_ms:.2f}ms")
            
            # Clean up
            del self.active_operations[operation_id]
    
    def record_batch_submission(self, seat_count: int, submission_time_ms: float):
        """Record batch submission performance"""
        with self.lock:
            self.batch_submissions = getattr(self, 'batch_submissions', [])
            self.batch_submissions.append({
                'seat_count': seat_count,
                'submission_time_ms': submission_time_ms,
                'timestamp': time.time()
            })

            # Keep only recent submissions
            if len(self.batch_submissions) > 100:
                self.batch_submissions = self.batch_submissions[-100:]

    def get_current_speed(self) -> float:
        """Get current seats per second speed"""
        with self.lock:
            if not hasattr(self, 'batch_submissions') or not self.batch_submissions:
                return 0.0

            # Calculate speed from recent submissions
            recent_submissions = [s for s in self.batch_submissions if time.time() - s['timestamp'] < 10]
            if not recent_submissions:
                return 0.0

            total_seats = sum(s['seat_count'] for s in recent_submissions)
            total_time = sum(s['submission_time_ms'] for s in recent_submissions) / 1000  # Convert to seconds

            return total_seats / total_time if total_time > 0 else 0.0

    @property
    def successful_operations(self) -> int:
        """Get number of successful operations"""
        return self.successes

    @property
    def failed_operations(self) -> int:
        """Get number of failed operations"""
        return self.failures

    @property
    def total_operations(self) -> int:
        """Get total number of operations"""
        return self.attempts

    def get_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        with self.lock:
            def avg(deque_obj):
                return sum(deque_obj) / len(deque_obj) if deque_obj else 0

            return {
                'attempts': self.attempts,
                'successes': self.successes,
                'failures': self.failures,
                'success_rate': (self.successes / self.attempts * 100) if self.attempts > 0 else 0,
                'avg_total_time_ms': avg(self.total_response_times),
                'avg_ws_to_decision_ms': avg(self.websocket_to_decision),
                'avg_decision_to_request_ms': avg(self.decision_to_request),
                'avg_request_to_response_ms': avg(self.request_to_response),
                'token_failures': self.token_failures,
                'network_failures': self.network_failures,
                'active_operations': len(self.active_operations),
                'current_speed': self.get_current_speed()
            }

class PersistentConnection:
    """Ultra-fast persistent HTTP connection for minimal overhead"""

    def __init__(self, host: str, port: int = 443, use_ssl: bool = True):
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.socket = None
        self.ssl_socket = None
        self.lock = threading.RLock()
        self.connected = False
        self.last_used = time.time()

        # Connect immediately
        self._connect()

    def _connect(self):
        """Establish persistent connection"""
        try:
            # Create raw socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)

            # Connect to host
            self.socket.connect((self.host, self.port))

            if self.use_ssl:
                # Wrap with SSL
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                self.ssl_socket = context.wrap_socket(self.socket, server_hostname=self.host)
                self.active_socket = self.ssl_socket
            else:
                self.active_socket = self.socket

            self.connected = True
            self.last_used = time.time()
            logger.debug(f"🔗 Persistent connection established to {self.host}:{self.port}")

        except Exception as e:
            logger.error(f"❌ Failed to establish persistent connection: {e}")
            self.connected = False
            self._cleanup()

    def send_request(self, method: str, path: str, headers: Dict[str, str], body: str = "") -> tuple:
        """Send HTTP request over persistent connection"""
        with self.lock:
            if not self.connected:
                self._connect()
                if not self.connected:
                    return None, "Connection failed"

            try:
                # Build HTTP request
                request_lines = [f"{method} {path} HTTP/1.1"]

                # Add headers
                for key, value in headers.items():
                    request_lines.append(f"{key}: {value}")

                # Add content length if body
                if body:
                    request_lines.append(f"Content-Length: {len(body.encode('utf-8'))}")

                request_lines.append("")  # Empty line
                if body:
                    request_lines.append(body)

                request_data = "\r\n".join(request_lines).encode('utf-8')

                # Send request
                self.active_socket.sendall(request_data)

                # Read response
                response_data = b""
                while True:
                    chunk = self.active_socket.recv(4096)
                    if not chunk:
                        break
                    response_data += chunk

                    # Check if we have complete response
                    if b"\r\n\r\n" in response_data:
                        # Parse headers to check content length
                        header_end = response_data.find(b"\r\n\r\n")
                        headers_part = response_data[:header_end].decode('utf-8')
                        body_part = response_data[header_end + 4:]

                        # Extract status code
                        status_line = headers_part.split('\r\n')[0]
                        status_code = int(status_line.split()[1])

                        # For 204 No Content, we're done
                        if status_code == 204:
                            break

                        # Check content length
                        content_length = 0
                        for line in headers_part.split('\r\n')[1:]:
                            if line.lower().startswith('content-length:'):
                                content_length = int(line.split(':')[1].strip())
                                break

                        if len(body_part) >= content_length:
                            break

                # Parse response
                if b"\r\n\r\n" in response_data:
                    header_end = response_data.find(b"\r\n\r\n")
                    headers_part = response_data[:header_end].decode('utf-8')
                    body_part = response_data[header_end + 4:].decode('utf-8')

                    status_line = headers_part.split('\r\n')[0]
                    status_code = int(status_line.split()[1])

                    self.last_used = time.time()
                    return status_code, body_part
                else:
                    return None, "Invalid response"

            except Exception as e:
                logger.error(f"❌ Request failed on persistent connection: {e}")
                self.connected = False
                self._cleanup()
                return None, str(e)

    def _cleanup(self):
        """Clean up connection"""
        try:
            if self.ssl_socket:
                self.ssl_socket.close()
            if self.socket:
                self.socket.close()
        except:
            pass
        self.socket = None
        self.ssl_socket = None
        self.active_socket = None

    def close(self):
        """Close connection"""
        with self.lock:
            self.connected = False
            self._cleanup()

class ConnectionPool:
    """Pool of persistent connections for maximum performance"""

    def __init__(self, host: str, pool_size: int = 10):
        self.host = host
        self.pool_size = pool_size
        self.connections = deque()
        self.lock = threading.RLock()

        # Pre-create connections
        for _ in range(pool_size):
            conn = PersistentConnection(host)
            if conn.connected:
                self.connections.append(conn)

        logger.info(f"🔗 Connection pool created: {len(self.connections)}/{pool_size} connections to {host}")

    def get_connection(self) -> Optional[PersistentConnection]:
        """Get a connection from the pool"""
        with self.lock:
            # Try to get a working connection
            for _ in range(len(self.connections)):
                if self.connections:
                    conn = self.connections.popleft()
                    if conn.connected:
                        return conn
                    else:
                        # Try to reconnect
                        conn._connect()
                        if conn.connected:
                            return conn

            # No connections available, create new one
            conn = PersistentConnection(self.host)
            if conn.connected:
                return conn

            return None

    def return_connection(self, conn: PersistentConnection):
        """Return connection to pool"""
        with self.lock:
            if conn.connected and len(self.connections) < self.pool_size:
                self.connections.append(conn)
            else:
                conn.close()

    def get_status(self) -> Dict[str, Any]:
        """Get current connection pool status"""
        with self.lock:
            return {
                'available_connections': len(self.connections),
                'target_connections': self.pool_size,
                'ready': len(self.connections) >= self.pool_size
            }

    def cleanup(self):
        """Clean up all connections"""
        with self.lock:
            while self.connections:
                conn = self.connections.popleft()
                conn.close()

class TokenPool:
    """Ultra-fast token pool with instant background generation"""

    def __init__(self, initial_pool_size: int = TOKEN_POOL_SIZE):
        self.pool_size = initial_pool_size
        self.tokens = deque()
        self.token_expiry = {}
        self.token_usage_count = {}  # token -> number of times used
        self.lock = threading.RLock()
        self.target_tickets = 0
        self.generation_in_progress = False

        # High-speed background generation
        self.generation_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="TokenGen")

        # Start background refresh loop
        self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self.refresh_thread.start()

    def set_target_tickets(self, tickets_wanted: int):
        """Set target tickets and immediately start background generation"""
        with self.lock:
            self.target_tickets = tickets_wanted
            tokens_needed = (tickets_wanted + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN + 1
            self.pool_size = max(TOKEN_POOL_SIZE, tokens_needed)
            current_tokens = len(self.tokens)

        # Start immediate background generation if needed
        if current_tokens < tokens_needed:
            needed = tokens_needed - current_tokens
            logger.info(f"🚀 INSTANT: Need {needed} more tokens for {tickets_wanted} tickets")
            # Generate more aggressively for high concurrency
            batch_size = min(needed, 20)  # Generate up to 20 tokens at once
            self._generate_tokens_background(batch_size)
        else:
            logger.info(f"✅ READY: {current_tokens} tokens available for {tickets_wanted} tickets")
    
    def get_token(self) -> Optional[str]:
        """Get a valid token from the pool - tokens can be used up to SEATS_PER_TOKEN times"""
        with self.lock:
            # Remove expired or fully used tokens
            current_time = time.time()
            while self.tokens:
                token = self.tokens[0]

                # Check if token is expired
                if token not in self.token_expiry or self.token_expiry[token] <= current_time + TOKEN_REFRESH_THRESHOLD:
                    # Calculate time left before removing
                    time_left = self.token_expiry.get(token, 0) - current_time if token in self.token_expiry else 0

                    # Token expired or about to expire, remove it
                    expired_token = self.tokens.popleft()
                    self.token_expiry.pop(expired_token, None)
                    self.token_usage_count.pop(expired_token, None)
                    logger.info(f"🗑️ Removed expired token {expired_token[:8]}... (had {max(0, int(time_left))} seconds left)")
                    continue

                # Check if token has been used too many times
                usage_count = self.token_usage_count.get(token, 0)
                if usage_count >= SEATS_PER_TOKEN:
                    # Token fully used, remove it
                    used_token = self.tokens.popleft()
                    self.token_expiry.pop(used_token, None)
                    self.token_usage_count.pop(used_token, None)
                    logger.info(f"✅ Token {used_token[:8]}... completed its lifecycle (used {usage_count}/{SEATS_PER_TOKEN} times)")
                    continue

                # Token is valid and not fully used - increment usage and return
                self.token_usage_count[token] = usage_count + 1
                logger.info(f"🔄 Reusing token {token[:8]}... (usage {self.token_usage_count[token]}/{SEATS_PER_TOKEN})")
                return token

            # No valid tokens available
            logger.warning("⚠️ No valid tokens available in pool")
            return None

    def _is_token_expired(self, token: str) -> bool:
        """Check if a token is expired or about to expire"""
        if token not in self.token_expiry:
            return True
        current_time = time.time()
        return self.token_expiry[token] <= current_time + TOKEN_REFRESH_THRESHOLD

    def _renew_expiring_tokens(self):
        """Check for tokens that are about to expire and generate replacements"""
        try:
            current_time = time.time()
            tokens_to_renew = []

            with self.lock:
                for token in list(self.tokens):
                    if token in self.token_expiry:
                        time_left = self.token_expiry[token] - current_time
                        # If token expires in less than 5 minutes, mark for renewal
                        if time_left <= TOKEN_REFRESH_THRESHOLD:
                            tokens_to_renew.append(token)

            if tokens_to_renew:
                logger.info(f"🔄 Renewing {len(tokens_to_renew)} expiring tokens...")

                # Generate replacement tokens
                event_id = get_cached_event_id()
                if event_id:
                    # Generate new tokens to replace expiring ones
                    self._generate_tokens_background(len(tokens_to_renew))

                    # Remove the expiring tokens after generating replacements
                    with self.lock:
                        for token in tokens_to_renew:
                            if token in self.tokens:
                                self.tokens.remove(token)
                                self.token_expiry.pop(token, None)
                                self.token_usage_count.pop(token, None)
                                logger.info(f"🔄 Removed expiring token {token[:8]}...")

        except Exception as e:
            logger.error(f"❌ Error renewing expiring tokens: {e}")
    
    def _refresh_loop(self):
        """Background thread to keep token pool filled based on tickets wanted and renew expiring tokens"""
        while True:
            try:
                # First, check for tokens that need renewal
                self._renew_expiring_tokens()

                # Check if we need to generate tokens (quick lock check)
                should_generate = False
                with self.lock:
                    # Only generate tokens if we actually need them for target tickets
                    if self.target_tickets <= 0:
                        pass  # Will sleep outside the lock
                    else:
                        # Calculate actual tokens needed based on seats per token
                        tokens_needed = (self.target_tickets + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN + 1
                        current_tokens = len(self.tokens)
                        needed_tokens = tokens_needed - current_tokens

                        if needed_tokens > 0 and not self.generation_in_progress:
                            self.generation_in_progress = True
                            should_generate = True

                # Sleep outside the lock to avoid blocking other operations
                if not should_generate:
                    time.sleep(1)
                    continue

                # Get event ID
                event_id = get_cached_event_id()
                if not event_id:
                    with self.lock:
                        self.generation_in_progress = False
                    time.sleep(5)
                    continue

                # Create tokens asynchronously - batch them for efficiency
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Create tokens more aggressively for high concurrency needs
                    batch_size = min(needed_tokens, 15)  # Larger batches for high concurrency
                    tasks = [get_hold_token(event_id) for _ in range(batch_size)]
                    new_tokens = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

                    # Add valid tokens to pool
                    current_time = time.time()
                    added_count = 0

                    with self.lock:
                        for token in new_tokens:
                            if isinstance(token, str) and token:
                                self.tokens.append(token)
                                # Get actual expiry time from seatsio API
                                try:
                                    # Create new event loop for this thread
                                    token_loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(token_loop)
                                    try:
                                        actual_time_left = token_loop.run_until_complete(check_token_time_remaining(token))
                                        self.token_expiry[token] = current_time + actual_time_left
                                        logger.info(f"✅ Token {token[:8]}... expires in {actual_time_left} seconds (real API check)")
                                    finally:
                                        token_loop.close()
                                except Exception as e:
                                    logger.warning(f"Failed to check real expiry for token {token[:8]}..., using default: {e}")
                                    self.token_expiry[token] = current_time + TOKEN_EXPIRY_SECONDS

                                self.token_usage_count[token] = 0  # Initialize usage count
                                added_count += 1

                        total_tokens = len(self.tokens)
                        tokens_needed = (self.target_tickets + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN + 1
                        logger.info(f"🔄 Token pool refreshed: +{added_count} tokens, {total_tokens}/{tokens_needed} needed")

                        # If we have target tickets set, log progress
                        if self.target_tickets > 0:
                            coverage = (total_tokens / self.target_tickets) * 100
                            logger.info(f"🎯 Token coverage: {coverage:.1f}% for {self.target_tickets} target tickets")

                finally:
                    # Clean up the main event loop if it exists
                    try:
                        if 'loop' in locals() and loop and not loop.is_closed():
                            loop.close()
                    except:
                        pass  # Ignore cleanup errors

                    with self.lock:
                        self.generation_in_progress = False

            except Exception as e:
                logger.error(f"Error refreshing token pool: {e}")
                import traceback
                logger.debug(f"Token pool refresh traceback: {traceback.format_exc()}")
                with self.lock:
                    self.generation_in_progress = False

            time.sleep(2)  # Check every 2 seconds for faster response

    def _generate_tokens_background(self, count: int):
        """Generate tokens in background without blocking"""
        if self.generation_in_progress:
            return

        def generate_async():
            try:
                self.generation_in_progress = True
                logger.info(f"🔄 Background generating {count} tokens...")

                # Get event ID
                event_id = get_cached_event_id()
                if not event_id:
                    logger.error("❌ No event ID for background generation")
                    return

                # Generate tokens in parallel
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Create tasks for parallel generation
                    tasks = [get_hold_token(event_id) for _ in range(count)]
                    new_tokens = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

                    # Add valid tokens
                    current_time = time.time()
                    added_count = 0

                    with self.lock:
                        for token in new_tokens:
                            if isinstance(token, str) and token:
                                self.tokens.append(token)
                                # Get actual expiry time from seatsio API
                                try:
                                    actual_time_left = loop.run_until_complete(check_token_time_remaining(token))
                                    self.token_expiry[token] = current_time + actual_time_left
                                    logger.info(f"✅ Background token {token[:8]}... expires in {actual_time_left} seconds (real API check)")
                                except Exception as e:
                                    logger.warning(f"Failed to check real expiry for background token {token[:8]}..., using default: {e}")
                                    self.token_expiry[token] = current_time + TOKEN_EXPIRY_SECONDS

                                self.token_usage_count[token] = 0  # Initialize usage count
                                added_count += 1

                    logger.info(f"✅ Background generated {added_count}/{count} tokens in background")

                finally:
                    loop.close()

            except Exception as e:
                logger.error(f"❌ Background generation error: {e}")
            finally:
                self.generation_in_progress = False

        # Submit to background executor
        self.generation_executor.submit(generate_async)

    def force_token_generation(self, count: int) -> int:
        """Force immediate generation of tokens (blocking)"""
        logger.info(f"🔄 Force generating {count} tokens...")

        # Get event ID
        event_id = get_cached_event_id()
        if not event_id:
            logger.error("❌ No event ID available for token generation")
            return 0

        # Generate tokens synchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            tasks = [get_hold_token(event_id) for _ in range(count)]
            new_tokens = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

            # Add valid tokens to pool
            current_time = time.time()
            added_count = 0

            with self.lock:
                for token in new_tokens:
                    if isinstance(token, str) and token:
                        self.tokens.append(token)
                        # Get actual expiry time from seatsio API
                        try:
                            # Create new event loop for this thread
                            token_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(token_loop)
                            try:
                                actual_time_left = token_loop.run_until_complete(check_token_time_remaining(token))
                                self.token_expiry[token] = current_time + actual_time_left
                                logger.info(f"✅ Generated token {token[:8]}... expires in {actual_time_left} seconds (real API check) ({added_count+1}/{count})")
                            finally:
                                token_loop.close()
                        except Exception as e:
                            logger.warning(f"Failed to check real expiry for generated token {token[:8]}..., using default: {e}")
                            self.token_expiry[token] = current_time + TOKEN_EXPIRY_SECONDS
                            logger.info(f"✅ Generated token {token[:8]}... with default expiry ({added_count+1}/{count})")

                        self.token_usage_count[token] = 0  # Initialize usage count
                        added_count += 1

            logger.info(f"🔄 Force generation complete: {added_count}/{count} tokens created")
            return added_count

        except Exception as e:
            logger.error(f"❌ Error in force token generation: {e}")
            return 0
        finally:
            loop.close()

    def set_target_pool_size(self, target_size: int):
        """Set target pool size for token preparation"""
        with self.lock:
            self.pool_size = target_size
            logger.info(f"🎯 Token pool target set to {target_size}")

    def prepare_tokens(self, required_tokens: int):
        """Prepare the specified number of tokens"""
        with self.lock:
            current_tokens = len(self.tokens)
            if current_tokens < required_tokens:
                needed = required_tokens - current_tokens
                self.generate_tokens_fast(needed)

    def generate_tokens_fast(self, count: int):
        """Generate tokens quickly in background"""
        if self.generation_in_progress:
            return

        self.generation_in_progress = True

        def generate_batch():
            try:
                for _ in range(count):
                    if len(self.tokens) >= self.pool_size:
                        break
                    self._generate_single_token()
            finally:
                self.generation_in_progress = False

        self.generation_executor.submit(generate_batch)

    def get_multiple_tokens(self, count: int) -> List[str]:
        """Get multiple tokens at once for batch operations - each token can be used multiple times"""
        tokens = []
        with self.lock:
            # For batch operations, we need to be smarter about token allocation
            # Convert to list for easier manipulation
            token_list = list(self.tokens)

            for token in token_list:
                if self._is_token_expired(token):
                    # Remove expired token
                    self.tokens.remove(token)
                    self.token_expiry.pop(token, None)
                    self.token_usage_count.pop(token, None)
                    continue

                usage_count = self.token_usage_count.get(token, 0)
                remaining_capacity = SEATS_PER_TOKEN - usage_count

                if remaining_capacity > 0:
                    # This token can handle more seats
                    seats_to_use = min(remaining_capacity, count - len(tokens))
                    for _ in range(seats_to_use):
                        tokens.append(token)
                        self.token_usage_count[token] = self.token_usage_count.get(token, 0) + 1

                    logger.info(f"🔄 Batch allocated {seats_to_use} seats to token {token[:8]}... (now {self.token_usage_count[token]}/{SEATS_PER_TOKEN})")

                    # If token is now fully used, remove it from the pool
                    if self.token_usage_count[token] >= SEATS_PER_TOKEN:
                        self.tokens.remove(token)
                        self.token_expiry.pop(token, None)
                        self.token_usage_count.pop(token, None)
                        logger.info(f"✅ Token {token[:8]}... completed its lifecycle after batch allocation")

                if len(tokens) >= count:
                    break

        # If we need more tokens, generate them in background
        if len(tokens) < count:
            needed = count - len(tokens)
            self.generate_tokens_fast(needed)

        return tokens

    def get_batch_tokens(self, required_tokens: int) -> List[str]:
        """
        Get exact number of fresh tokens for batch operations
        Each token will be used for exactly 50 seats in one request
        """
        tokens = []
        with self.lock:
            current_time = time.time()

            # Get fresh tokens that haven't been used yet
            available_tokens = list(self.tokens)

            for token in available_tokens:
                if len(tokens) >= required_tokens:
                    break

                # Check if token is expired
                if token not in self.token_expiry or self.token_expiry[token] <= current_time + TOKEN_REFRESH_THRESHOLD:
                    # Remove expired token
                    self.tokens.remove(token)
                    self.token_expiry.pop(token, None)
                    self.token_usage_count.pop(token, None)
                    continue

                # For batch operations, prefer unused tokens
                usage_count = self.token_usage_count.get(token, 0)
                if usage_count == 0:  # Fresh token
                    tokens.append(token)
                    # Remove from pool since it will be fully used (50 seats)
                    self.tokens.remove(token)
                    # Mark as fully used
                    self.token_usage_count[token] = SEATS_PER_TOKEN
                    logger.info(f"🎯 Allocated fresh token {token[:8]}... for batch operation")

            # If we don't have enough fresh tokens, use partially used ones
            if len(tokens) < required_tokens:
                for token in list(self.tokens):
                    if len(tokens) >= required_tokens:
                        break

                    usage_count = self.token_usage_count.get(token, 0)
                    remaining_capacity = SEATS_PER_TOKEN - usage_count

                    if remaining_capacity >= 50:  # Can handle a full batch
                        tokens.append(token)
                        # Remove from pool and mark as fully used
                        self.tokens.remove(token)
                        self.token_usage_count[token] = SEATS_PER_TOKEN
                        logger.info(f"🔄 Allocated token {token[:8]}... with {remaining_capacity} remaining capacity")

        logger.info(f"🎯 BATCH TOKENS: Got {len(tokens)}/{required_tokens} tokens for batch operations")
        return tokens

    def get_unique_tokens_for_batching(self, max_tokens: int) -> List[str]:
        """
        Get unique tokens for batch operations without consuming them
        Returns actual unique token strings that can be used for multiple concurrent requests
        """
        unique_tokens = []
        with self.lock:
            current_time = time.time()
            token_list = list(self.tokens)

            for token in token_list:
                if len(unique_tokens) >= max_tokens:
                    break

                # Check if token is expired
                if self._is_token_expired(token):
                    self.tokens.remove(token)
                    self.token_expiry.pop(token, None)
                    self.token_usage_count.pop(token, None)
                    continue

                # Check if token has remaining capacity
                usage_count = self.token_usage_count.get(token, 0)
                if usage_count < SEATS_PER_TOKEN:
                    unique_tokens.append(token)

        logger.info(f"🎯 UNIQUE TOKENS: Got {len(unique_tokens)} unique tokens for batching")
        return unique_tokens

    def get_status(self) -> Dict[str, Any]:
        """Get current token pool status"""
        with self.lock:
            # Calculate total remaining capacity across all tokens
            total_capacity = 0
            for token in self.tokens:
                if not self._is_token_expired(token):
                    usage_count = self.token_usage_count.get(token, 0)
                    remaining = SEATS_PER_TOKEN - usage_count
                    total_capacity += remaining

            return {
                'available_tokens': len(self.tokens),
                'target_tokens': self.pool_size,
                'total_seat_capacity': total_capacity,
                'generation_in_progress': self.generation_in_progress,
                'ready': len(self.tokens) >= self.pool_size
            }

class AutoHoldSystem:
    """Ultra-high performance auto-hold system"""
    
    def __init__(self, event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None):
        self.event_key = event_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy

        # Performance tracking
        self.performance = PerformanceTracker()

        # Token management
        self.token_pool = TokenPool()

        # Account identity management for unique user agents and device IDs
        self.identity_manager = get_account_identity_manager()
        self.current_account_info = None  # Will be set when we get account info

        # No connection pool needed - using proxy-aware HTTP requests for Webshare rotation

        # Thread pool for concurrent operations - MASSIVELY INCREASED FOR ZERO QUEUING
        self.executor = ThreadPoolExecutor(max_workers=500, thread_name_prefix="AutoHold")

        # Debug mode for detailed request/response logging
        self.debug_mode = False

        # Shared HTTP client for efficient proxy requests
        self._http_client = None
        self._client_lock = threading.Lock()

        # Pre-built request template
        self.request_template = {
            'events': [self.event_key],
            'holdToken': None,
            'objects': [{'objectId': None}],
            'channelKeys': build_channel_keys(self.channel_keys, self.team_id),
            'validateEventsLinkedToSameChart': True,
        }
        
        # Pre-built headers template
        self.headers_template = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'x-client-tool': 'Renderer',
            'x-browser-id': None,
            'x-signature': None
        }
        
        # Callbacks
        self.success_callback: Optional[Callable[[str, str], None]] = None
        self.failure_callback: Optional[Callable[[str, str], None]] = None
        
        logger.info(f"🚀 AutoHoldSystem initialized for event {event_key} with proxy rotation {'ENABLED' if proxy else 'DISABLED'}")

    def _get_account_headers(self) -> Dict[str, str]:
        """Get headers with account-specific identity information"""
        # Get fresh account info if we don't have it or it's old
        if not self.current_account_info:
            from account_token_manager import get_account_info
            self.current_account_info = get_account_info()

        headers = self.headers_template.copy()

        if self.current_account_info:
            # Use account-specific user agent and browser ID
            if 'user_agent' in self.current_account_info:
                headers['user-agent'] = self.current_account_info['user_agent']
            if 'browser_id' in self.current_account_info:
                headers['x-browser-id'] = self.current_account_info['browser_id']
        else:
            # Fallback to random browser ID if no account info
            headers['x-browser-id'] = secrets.token_hex(8)

        return headers

    def set_target_tickets(self, tickets_wanted: int):
        """Set target tickets - returns immediately, generation happens in background"""
        # This returns in <1ms, generation happens in background
        self.token_pool.set_target_tickets(tickets_wanted)
        logger.info(f"🚀 INSTANT: Auto-hold configured for {tickets_wanted} tickets (background generation started)")

    def get_token_status(self) -> dict:
        """Get current token status instantly (non-blocking)"""
        with self.token_pool.lock:
            available = len(self.token_pool.tokens)
            target = self.token_pool.pool_size
            tickets = self.token_pool.target_tickets

        return {
            'available_tokens': available,
            'target_tokens': target,
            'target_tickets': tickets,
            'ready': available >= target,
            'generation_in_progress': self.token_pool.generation_in_progress
        }

    def set_callbacks(self, success_callback: Callable[[str, str], None], failure_callback: Callable[[str, str], None]):
        """Set callbacks for success and failure events"""
        self.success_callback = success_callback
        self.failure_callback = failure_callback

    def set_debug_mode(self, enabled: bool):
        """Enable or disable debug mode for detailed request/response logging"""
        self.debug_mode = enabled
        logger.info(f"🔍 Debug mode: {'ENABLED' if enabled else 'DISABLED'}")

    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        return self.debug_mode

    def _get_http_client(self):
        """Get or create a shared HTTP client for efficient requests"""
        with self._client_lock:
            if self._http_client is None:
                import httpx
                client_kwargs = {
                    'http2': True,
                    'timeout': httpx.Timeout(10.0),
                    'verify': False,
                    'limits': httpx.Limits(max_connections=100, max_keepalive_connections=20)
                }
                if self.proxy:
                    # Parse proxy format: ip:port:username:password
                    parts = self.proxy.split(':')
                    if len(parts) == 4:
                        proxy_url = f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"
                        client_kwargs['proxy'] = proxy_url

                self._http_client = httpx.Client(**client_kwargs)
            return self._http_client

    def _make_sync_request(self, url: str, body_str: str, headers: Dict[str, str]):
        """Make synchronous HTTP request with shared client"""
        client = self._get_http_client()
        try:
            response = client.post(url, content=body_str, headers=headers)
            return response
        except Exception as e:
            # If client fails, recreate it and try once more
            with self._client_lock:
                if self._http_client:
                    try:
                        self._http_client.close()
                    except:
                        pass
                    self._http_client = None

            # Retry with fresh client
            client = self._get_http_client()
            response = client.post(url, content=body_str, headers=headers)
            return response

    def hold_seat(self, seat_id: str, websocket_timestamp: Optional[float] = None) -> bool:
        """
        Ultra-fast seat holding - main entry point
        Returns immediately, actual hold happens asynchronously
        """
        # Start performance tracking
        operation_id = self.performance.start_operation(seat_id, websocket_timestamp)

        # Record decision time (immediate)
        self.performance.record_decision(operation_id)

        # Submit to thread pool for immediate return
        self.executor.submit(self._hold_seat_sync, seat_id, operation_id)

        return True

    def _hold_seat_sync(self, seat_id: str, operation_id: str):
        """Ultra-fast seat holding with proxy rotation - MINIMAL OVERHEAD"""
        token = None

        try:
            # Get token from pool (pre-cached, instant) - NO DEBUG LOGGING FOR SPEED
            token = self.token_pool.get_token()

            if not token:
                self.performance.complete_operation(operation_id, False, 'token')
                if self.failure_callback:
                    self.failure_callback(seat_id, "No tokens available")
                return False

            # No connection needed - using proxy-aware HTTP requests

            # Build request data (minimal overhead)
            request_data = self.request_template.copy()
            request_data['holdToken'] = token
            request_data['objects'] = [{'objectId': seat_id}]

            # Build headers with account-specific identity (minimal overhead)
            headers = self._get_account_headers()

            # Serialize and sign (minimal overhead)
            body_str = json.dumps(request_data, separators=(',', ':'))
            headers['x-signature'] = generate_x_signature(body_str)

            # Define the API path
            path = '/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'

            # Debug logging if enabled
            if self.debug_mode:
                logger.info(f"🔍 DEBUG REQUEST [{seat_id}]:")
                logger.info(f"   URL: https://{SEATSIO_IP}{path}")
                logger.info(f"   Headers: {headers}")
                logger.info(f"   Body: {body_str}")

            # Record request start
            self.performance.record_request_start(operation_id)

            # CRITICAL: Send via optimized proxy-aware HTTP request (WEBSHARE ROTATION)
            url = f'https://{SEATSIO_IP}{path}'

            # Use shared HTTP client for efficient requests
            response = self._make_sync_request(url, body_str, headers)
            status_code = response.status_code
            response_body = response.text

            # Debug logging if enabled
            if self.debug_mode:
                logger.info(f"🔍 DEBUG RESPONSE [{seat_id}]:")
                logger.info(f"   Status: {status_code}")
                logger.info(f"   Body: {response_body}")

            # Process response
            if status_code == 204:
                self.performance.complete_operation(operation_id, True)
                if self.success_callback:
                    self.success_callback(seat_id, token)
                return True
            else:
                error_msg = f"HTTP {status_code}: {response_body[:100] if response_body else 'No response'}"

                # Enhanced debug logging for non-204 responses
                if self.debug_mode:
                    logger.error(f"🔍 DEBUG FAILURE [{seat_id}]: {error_msg}")
                    logger.error(f"🔍 DEBUG FULL RESPONSE: {response_body}")

                self.performance.complete_operation(operation_id, False, 'network')
                if self.failure_callback:
                    self.failure_callback(seat_id, error_msg)
                return False

        except Exception as e:
            error_msg = f"Exception: {str(e)}"

            # Enhanced debug logging for exceptions
            if self.debug_mode:
                logger.error(f"🔍 DEBUG EXCEPTION [{seat_id}]: {error_msg}")
                import traceback
                logger.error(f"🔍 DEBUG TRACEBACK: {traceback.format_exc()}")

            self.performance.complete_operation(operation_id, False, 'network')
            if self.failure_callback:
                self.failure_callback(seat_id, error_msg)
            return False

        finally:
            # No connection cleanup needed - using proxy-aware HTTP requests
            pass

    def hold_multiple_seats_OLD_BATCH(self, seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
        """
        Hold multiple seats with MAXIMUM CONCURRENCY
        Each seat gets its own individual request for maximum reliability
        If any single seat fails, others can still succeed
        """
        if len(seat_ids) > MAX_CONCURRENT_HOLDS:
            logger.warning(f"Requested {len(seat_ids)} seats, limiting to {MAX_CONCURRENT_HOLDS}")
            seat_ids = seat_ids[:MAX_CONCURRENT_HOLDS]

        start_time = time.perf_counter()
        total_seats = len(seat_ids)

        # Calculate how many unique tokens we need (each can handle 50 seats)
        tokens_needed = (total_seats + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN

        # Get unique tokens for batch operations
        unique_tokens = self.token_pool.get_unique_tokens_for_batching(tokens_needed)
        if not unique_tokens:
            logger.error(f"❌ No tokens available for batch operation!")
            return False

        logger.info(f"� HIGH CONCURRENCY HOLD: {total_seats} seats using {len(unique_tokens)} tokens")

        # Create batches ensuring each token doesn't exceed 50 seats total
        seat_batches = []
        token_usage = {token: 0 for token in unique_tokens}  # Track seats per token

        # Process seats in chunks, ensuring no token exceeds 50 seats
        remaining_seats = seat_ids.copy()
        while remaining_seats:
            # Find a token that can handle more seats
            available_token = None
            for token in unique_tokens:
                if token_usage[token] < 50:
                    available_token = token
                    break

            if not available_token:
                logger.warning(f"⚠️ All tokens at capacity, stopping at {len(seat_ids) - len(remaining_seats)} seats")
                break

            # Determine batch size for this token
            token_remaining_capacity = 50 - token_usage[available_token]
            batch_size = min(10, token_remaining_capacity, len(remaining_seats))  # Max 10 seats per request

            # Create batch
            batch_seats = remaining_seats[:batch_size]
            remaining_seats = remaining_seats[batch_size:]

            seat_batches.append((available_token, batch_seats))
            token_usage[available_token] += batch_size

            logger.debug(f"🎯 Batch: {batch_size} seats → token {available_token[:8]}... (now {token_usage[available_token]}/50)")

        logger.info(f"🎯 Created {len(seat_batches)} concurrent batches (avg {batch_size} seats/batch)")

        # Submit all batches concurrently for MAXIMUM PARALLELISM
        futures = []
        for token, batch_seats in seat_batches:
            future = self.executor.submit(self._hold_batch_sync, token, batch_seats, websocket_timestamp)
            futures.append(future)

        submission_time = (time.perf_counter() - start_time) * 1000
        logger.info(f"⚡ ALL BATCHES SUBMITTED in {submission_time:.1f}ms - {len(seat_batches)} concurrent requests!")

        # Wait for all futures to complete (optional - for debugging)
        if len(futures) <= 20:  # Only wait for smaller batches to avoid blocking
            try:
                completed = 0
                for future in futures:
                    if future.result(timeout=10):  # 10 second timeout per batch
                        completed += 1
                logger.info(f"✅ BATCH COMPLETION: {completed}/{len(futures)} batches completed successfully")
            except Exception as e:
                logger.warning(f"⚠️ Error waiting for batch completion: {str(e)}")

        # Update token usage counts in the pool
        with self.token_pool.lock:
            for token, seats_used in token_usage.items():
                if seats_used > 0:
                    current_usage = self.token_pool.token_usage_count.get(token, 0)
                    new_usage = current_usage + seats_used
                    self.token_pool.token_usage_count[token] = new_usage

                    logger.debug(f"🔄 Updated token {token[:8]}... usage: {current_usage} → {new_usage}")

                    # If token is now fully used, remove it from the pool
                    if new_usage >= SEATS_PER_TOKEN:
                        if token in self.token_pool.tokens:
                            self.token_pool.tokens.remove(token)
                            self.token_pool.token_expiry.pop(token, None)
                            self.token_pool.token_usage_count.pop(token, None)
                            logger.info(f"✅ Token {token[:8]}... completed its lifecycle after batch operation")

        # Update performance stats
        self.performance.record_batch_submission(total_seats, submission_time)

        return True

    def hold_multiple_seats(self, seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
        """
        Hold multiple seats with MAXIMUM CONCURRENCY
        Each seat gets its own individual request for maximum reliability
        If any single seat fails, others can still succeed
        """
        if len(seat_ids) > MAX_CONCURRENT_HOLDS:
            logger.warning(f"Requested {len(seat_ids)} seats, limiting to {MAX_CONCURRENT_HOLDS}")
            seat_ids = seat_ids[:MAX_CONCURRENT_HOLDS]

        start_time = time.perf_counter()
        total_seats = len(seat_ids)

        logger.info(f"🚀 INDIVIDUAL SEAT HOLD: {total_seats} seats with individual requests")

        # Submit individual seat hold requests concurrently
        futures = []
        for seat_id in seat_ids:
            # Create operation ID for performance tracking
            operation_id = self.performance.start_operation(seat_id, websocket_timestamp)
            self.performance.record_decision(operation_id)

            # Submit individual seat hold request
            future = self.executor.submit(self._hold_seat_sync, seat_id, operation_id)
            futures.append((future, seat_id, operation_id))

        submission_time = (time.perf_counter() - start_time) * 1000
        logger.info(f"⚡ ALL INDIVIDUAL REQUESTS SUBMITTED in {submission_time:.1f}ms - {len(futures)} concurrent requests!")

        # Wait for all futures to complete (optional - for debugging)
        if len(futures) <= 100:  # Wait for reasonable batch sizes
            try:
                completed = 0
                failed = 0
                for future, seat_id, operation_id in futures:
                    try:
                        if future.result(timeout=10):  # 10 second timeout per request
                            completed += 1
                        else:
                            failed += 1
                    except Exception as e:
                        failed += 1
                        logger.debug(f"❌ Seat {seat_id} failed: {str(e)}")

                logger.info(f"✅ INDIVIDUAL COMPLETION: {completed}/{len(futures)} seats held successfully, {failed} failed")
            except Exception as e:
                logger.warning(f"⚠️ Error waiting for individual completions: {str(e)}")

        # Update performance stats
        self.performance.record_batch_submission(total_seats, submission_time)

        return True

    def _hold_batch_sync(self, token: str, seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
        """
        Hold a batch of seats with a single token in ONE REQUEST
        This is the core optimization - one API call per batch instead of individual calls
        """
        batch_size = len(seat_ids)

        try:
            logger.info(f"🔥 BATCH HOLD: {batch_size} seats with token {token[:8]}...")

            # Build request data for MULTIPLE SEATS in ONE REQUEST
            request_data = self.request_template.copy()
            request_data['holdToken'] = token
            # KEY CHANGE: Multiple seats in objects array
            request_data['objects'] = [{'objectId': seat_id} for seat_id in seat_ids]

            # Build headers with account-specific identity
            headers = self._get_account_headers()

            # Serialize and sign
            body_str = json.dumps(request_data, separators=(',', ':'))
            headers['x-signature'] = generate_x_signature(body_str)

            # Define the API path
            path = '/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'

            # Debug logging if enabled
            if self.debug_mode:
                logger.info(f"🔍 DEBUG BATCH REQUEST [{batch_size} seats]:")
                logger.info(f"   URL: https://{SEATSIO_IP}{path}")
                logger.info(f"   Headers: {headers}")
                logger.info(f"   Body: {body_str}")
            elif batch_size <= 10:  # Only log small batches that might cause issues
                logger.debug(f"🔍 Small batch request: {batch_size} seats, token {token[:8]}..., seats: {seat_ids[:3]}...")

            # CRITICAL: Send ONE REQUEST for ALL SEATS in this batch via optimized proxy
            url = f'https://{SEATSIO_IP}{path}'

            # Use shared HTTP client for efficient batch requests
            response = self._make_sync_request(url, body_str, headers)
            status_code = response.status_code
            response_body = response.text

            # Debug logging if enabled
            if self.debug_mode:
                logger.info(f"🔍 DEBUG BATCH RESPONSE [{batch_size} seats]:")
                logger.info(f"   Status: {status_code}")
                logger.info(f"   Body: {response_body}")

            # Process response for entire batch - handle None status code from connection failures
            if status_code is None:
                error_msg = f"Connection error: {response_body}"
                if self.debug_mode:
                    logger.error(f"🔍 DEBUG BATCH CONNECTION ERROR [{batch_size} seats]: {error_msg}")
                logger.error(f"❌ BATCH FAILED: {batch_size} seats with token {token[:8]}... - {error_msg}")
                # Call failure callback for each seat in the batch
                if self.failure_callback:
                    for seat_id in seat_ids:
                        self.failure_callback(seat_id, error_msg)
                return False
            elif status_code == 204:
                logger.info(f"✅ BATCH SUCCESS: {batch_size} seats held with token {token[:8]}...")
                # Call success callback for each seat in the batch
                if self.success_callback:
                    for seat_id in seat_ids:
                        self.success_callback(seat_id, token)
                return True
            else:
                error_msg = f"HTTP {status_code}: {response_body[:200] if response_body else 'No response'}"
                logger.error(f"❌ BATCH FAILED: {batch_size} seats with token {token[:8]}... - {error_msg}")

                # Enhanced debug logging for non-204 responses
                if self.debug_mode:
                    logger.error(f"🔍 DEBUG BATCH FAILURE [{batch_size} seats]: {error_msg}")
                    logger.error(f"🔍 DEBUG BATCH FULL RESPONSE: {response_body}")

                # Additional debugging for 400 errors
                if status_code == 400:
                    logger.error(f"🔍 400 ERROR DEBUG: Batch size: {batch_size}, Seats: {seat_ids}, Token: {token[:12]}...")
                    logger.error(f"🔍 Request body length: {len(body_str)}, Objects count: {len(request_data['objects'])}")

                # Call failure callback for each seat in the batch
                if self.failure_callback:
                    for seat_id in seat_ids:
                        self.failure_callback(seat_id, error_msg)
                return False

        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            logger.error(f"❌ BATCH EXCEPTION: {batch_size} seats with token {token[:8]}... - {error_msg}")
            if self.failure_callback:
                for seat_id in seat_ids:
                    self.failure_callback(seat_id, error_msg)
            return False

        finally:
            # No connection cleanup needed - using proxy-aware HTTP requests
            pass

    def set_target_seats_count(self, target_seats: int):
        """Set target seats count for token preparation"""
        required_tokens = (target_seats + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN
        self.token_pool.set_target_pool_size(required_tokens)
        logger.info(f"🎯 Target set: {target_seats} seats → {required_tokens} tokens")

    def prepare_tokens_for_seats(self, seat_count: int):
        """Prepare tokens for the specified number of seats - optimized for batch holding"""
        required_tokens = (seat_count + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN

        # Use the new target system for better preparation
        self.token_pool.set_target_tickets(seat_count)

        logger.info(f"⚡ PREPARING: {required_tokens} tokens for {seat_count} seats (batch holding optimized)")

        # Force immediate generation if needed
        current_tokens = len(self.token_pool.tokens)
        if current_tokens < required_tokens:
            needed = required_tokens - current_tokens
            logger.info(f"🚀 FORCE GENERATING: {needed} additional tokens immediately")
            self.token_pool.generate_tokens_fast(needed)

    def get_preparation_status(self) -> Dict[str, Any]:
        """Get current preparation status"""
        token_status = self.token_pool.get_status()

        return {
            'tokens_ready': token_status.get('available_tokens', 0),
            'tokens_target': token_status.get('target_tokens', 0),
            'connections_ready': 1,  # Always ready with proxy-aware HTTP requests
            'connections_target': 1,  # No connection pool needed
            'ready': (token_status.get('available_tokens', 0) >= token_status.get('target_tokens', 0) and
                     token_status.get('target_tokens', 0) > 0)
        }

    def release_all_seats(self) -> int:
        """Release all currently held seats"""
        # This would need to be implemented based on your seat tracking system
        # For now, return 0 as placeholder
        logger.info("🔓 Release all seats requested")
        return 0

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics"""
        stats = self.performance.get_stats()

        # Add real-time metrics
        stats.update({
            'current_speed': self.performance.get_current_speed(),
            'successful_holds': self.performance.successful_operations,
            'failed_holds': self.performance.failed_operations,
            'total_operations': self.performance.total_operations
        })

        return stats

    def log_performance_summary(self):
        """Log a detailed performance summary"""
        stats = self.get_performance_stats()

        logger.info("📊 AUTO-HOLD PERFORMANCE SUMMARY")
        logger.info(f"   🎯 Total Attempts: {stats['attempts']}")
        logger.info(f"   ✅ Successes: {stats['successes']} ({stats['success_rate']:.1f}%)")
        logger.info(f"   ❌ Failures: {stats['failures']} (Token: {stats['token_failures']}, Network: {stats['network_failures']})")
        logger.info(f"   ⏱️  Average Total Time: {stats['avg_total_time_ms']:.2f}ms")
        logger.info(f"   📡 WebSocket→Decision: {stats['avg_ws_to_decision_ms']:.2f}ms")
        logger.info(f"   🔄 Decision→Request: {stats['avg_decision_to_request_ms']:.2f}ms")
        logger.info(f"   🌐 Request→Response: {stats['avg_request_to_response_ms']:.2f}ms")
        logger.info(f"   🔄 Active Operations: {stats['active_operations']}")

    def cleanup(self):
        """Clean up resources"""
        try:
            # Don't shutdown executor immediately to avoid "futures after shutdown" error
            # Just mark it for cleanup - it will be cleaned up when process exits
            if hasattr(self, 'executor') and not self.executor._shutdown:
                # Only shutdown if not already shutdown
                self.executor.shutdown(wait=False)
        except Exception as e:
            logger.warning(f"Error during executor cleanup: {e}")

        # Cleanup HTTP client
        try:
            with self._client_lock:
                if self._http_client:
                    self._http_client.close()
                    self._http_client = None
        except Exception as e:
            logger.warning(f"Error during HTTP client cleanup: {e}")

        logger.info("🧹 AutoHoldSystem cleaned up")

# Global instance for easy access
_auto_hold_system: Optional[AutoHoldSystem] = None

def initialize_auto_hold(event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None) -> AutoHoldSystem:
    """Initialize the global auto-hold system"""
    global _auto_hold_system

    if _auto_hold_system:
        _auto_hold_system.cleanup()

    _auto_hold_system = AutoHoldSystem(event_key, channel_keys, team_id, proxy)
    return _auto_hold_system

def get_auto_hold_system() -> Optional[AutoHoldSystem]:
    """Get the global auto-hold system instance"""
    return _auto_hold_system

def hold_seat_fast(seat_id: str, websocket_timestamp: Optional[float] = None) -> bool:
    """Fast seat holding using global system"""
    if _auto_hold_system:
        return _auto_hold_system.hold_seat(seat_id, websocket_timestamp)
    else:
        logger.error("Auto-hold system not initialized")
        return False

def hold_multiple_seats_fast(seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
    """Fast multiple seat holding using global system - TRUE BATCH HOLDING"""
    if _auto_hold_system:
        return _auto_hold_system.hold_multiple_seats(seat_ids, websocket_timestamp)
    else:
        logger.error("Auto-hold system not initialized")
        return False

def prepare_tokens_for_batch_holding(seat_count: int) -> bool:
    """Prepare tokens for batch holding - call this when tickets wanted changes"""
    if _auto_hold_system:
        _auto_hold_system.prepare_tokens_for_seats(seat_count)
        return True
    else:
        logger.error("Auto-hold system not initialized")
        return False

def get_batch_holding_status() -> Dict[str, Any]:
    """Get status of batch holding preparation"""
    if _auto_hold_system:
        return _auto_hold_system.token_pool.get_status()
    else:
        return {"error": "Auto-hold system not initialized"}

def get_performance_stats() -> Dict[str, Any]:
    """Get performance statistics from global system"""
    if _auto_hold_system:
        return _auto_hold_system.get_performance_stats()
    else:
        return {}

def set_debug_mode(enabled: bool) -> bool:
    """Enable or disable debug mode for detailed request/response logging"""
    if _auto_hold_system:
        _auto_hold_system.set_debug_mode(enabled)
        return True
    else:
        logger.error("Auto-hold system not initialized")
        return False

def is_debug_mode() -> bool:
    """Check if debug mode is enabled"""
    if _auto_hold_system:
        return _auto_hold_system.is_debug_mode()
    else:
        return False

def log_performance_summary():
    """Log performance summary from global system"""
    if _auto_hold_system:
        _auto_hold_system.log_performance_summary()
    else:
        logger.warning("Auto-hold system not initialized")
