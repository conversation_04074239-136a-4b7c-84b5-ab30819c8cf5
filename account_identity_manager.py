# account_identity_manager.py - Manages unique user agents and device IDs for each account

import json
import hashlib
import random
import logging
import os
from typing import Dict, Optional, Tuple
import threading

logger = logging.getLogger('webook_pro')

class AccountIdentityManager:
    """
    Manages unique user agents and device IDs for each account to avoid blocking.
    Each account gets a consistent identity that persists across sessions.
    """
    
    def __init__(self, identity_file: str = "account_identities.json"):
        """
        Initialize the account identity manager.
        
        Args:
            identity_file: Path to file storing account identities
        """
        self.identity_file = identity_file
        self.identities = {}  # email -> {user_agent, device_id, browser_id}
        self.identity_lock = threading.RLock()
        
        # Load existing identities
        self._load_identities()
    
    def _load_identities(self):
        """Load account identities from file"""
        try:
            if os.path.exists(self.identity_file):
                with open(self.identity_file, 'r') as f:
                    self.identities = json.load(f)
                logger.info(f"Loaded {len(self.identities)} account identities")
            else:
                logger.info("No existing account identities file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading account identities: {str(e)}")
            self.identities = {}
    
    def _save_identities(self):
        """Save account identities to file"""
        try:
            with open(self.identity_file, 'w') as f:
                json.dump(self.identities, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving account identities: {str(e)}")
    
    def _generate_user_agent(self, email: str) -> str:
        """
        Generate a unique but realistic user agent for an account.
        Uses email hash to ensure consistency.
        """
        # Create a seed from email for consistency
        seed = int(hashlib.md5(email.encode()).hexdigest()[:8], 16)
        random.seed(seed)
        
        # Browser versions and OS combinations
        browsers = [
            "Chrome/{}.0.0.0",
            "Chrome/{}.0.{}.0", 
            "Chrome/{}.0.{}.{}",
        ]
        
        chrome_versions = [130, 131, 132, 133, 134, 135]
        minor_versions = [0, 1, 2, 3, 4, 5]
        patch_versions = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
        
        os_systems = [
            "Windows NT 10.0; Win64; x64",
            "Windows NT 11.0; Win64; x64", 
            "Macintosh; Intel Mac OS X 10_15_7",
            "Macintosh; Intel Mac OS X 11_7_10",
            "X11; Linux x86_64"
        ]
        
        webkit_versions = [537, 538, 539]
        webkit_minor = [36, 37, 38]
        
        # Select components based on email hash
        browser_template = random.choice(browsers)
        chrome_version = random.choice(chrome_versions)
        os_system = random.choice(os_systems)
        webkit_version = random.choice(webkit_versions)
        webkit_min = random.choice(webkit_minor)
        
        # Format browser version
        if "{}.0.{}.0" in browser_template:
            minor_ver = random.choice(minor_versions)
            browser_version = browser_template.format(chrome_version, minor_ver)
        elif "{}.0.{}.{}" in browser_template:
            minor_ver = random.choice(minor_versions)
            patch_ver = random.choice(patch_versions)
            browser_version = browser_template.format(chrome_version, minor_ver, patch_ver)
        else:
            browser_version = browser_template.format(chrome_version)
        
        # Build user agent
        user_agent = f"Mozilla/5.0 ({os_system}) AppleWebKit/{webkit_version}.{webkit_min} (KHTML, like Gecko) {browser_version} Safari/{webkit_version}.{webkit_min}"
        
        # Reset random seed
        random.seed()
        
        return user_agent
    
    def _generate_device_id(self, email: str) -> str:
        """
        Generate a unique device ID for an account.
        Uses email hash to ensure consistency.
        """
        # Create a consistent device ID from email
        email_hash = hashlib.sha256(email.encode()).hexdigest()
        # Take first 16 characters and format as device ID
        return email_hash[:16]
    
    def _generate_browser_id(self, email: str) -> str:
        """
        Generate a unique browser ID for an account.
        Uses email hash to ensure consistency but different from device ID.
        """
        # Create a consistent browser ID from email + salt
        salted_email = f"{email}_browser_id"
        email_hash = hashlib.sha256(salted_email.encode()).hexdigest()
        # Take first 16 characters
        return email_hash[:16]
    
    def get_account_identity(self, email: str) -> Dict[str, str]:
        """
        Get or create identity information for an account.
        
        Args:
            email: Account email address
            
        Returns:
            Dictionary with user_agent, device_id, and browser_id
        """
        with self.identity_lock:
            if email not in self.identities:
                # Generate new identity for this account
                identity = {
                    'user_agent': self._generate_user_agent(email),
                    'device_id': self._generate_device_id(email),
                    'browser_id': self._generate_browser_id(email)
                }
                
                self.identities[email] = identity
                self._save_identities()
                
                logger.info(f"Generated new identity for account: {email}")
                logger.debug(f"User-Agent: {identity['user_agent']}")
                logger.debug(f"Device ID: {identity['device_id']}")
                logger.debug(f"Browser ID: {identity['browser_id']}")
            
            return self.identities[email].copy()
    
    def get_headers_for_account(self, email: str, base_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get headers with account-specific user agent and device ID.
        
        Args:
            email: Account email address
            base_headers: Base headers to extend (optional)
            
        Returns:
            Headers dictionary with account-specific values
        """
        identity = self.get_account_identity(email)
        
        headers = base_headers.copy() if base_headers else {}
        headers['user-agent'] = identity['user_agent']
        
        # Add device/browser IDs if not already present
        if 'x-browser-id' not in headers:
            headers['x-browser-id'] = identity['browser_id']
        if 'x-device-id' not in headers:
            headers['x-device-id'] = identity['device_id']
            
        return headers
    
    def get_status(self) -> Dict[str, int]:
        """Get status information about managed identities"""
        with self.identity_lock:
            return {
                'total_identities': len(self.identities),
                'identity_file_exists': os.path.exists(self.identity_file)
            }

# Global instance for easy access
_global_identity_manager = None
_identity_manager_lock = threading.Lock()

def get_account_identity_manager() -> AccountIdentityManager:
    """Get or create the global account identity manager"""
    global _global_identity_manager
    
    with _identity_manager_lock:
        if _global_identity_manager is None:
            _global_identity_manager = AccountIdentityManager()
        
        return _global_identity_manager

def get_account_identity(email: str) -> Dict[str, str]:
    """Convenience function to get account identity"""
    return get_account_identity_manager().get_account_identity(email)

def get_headers_for_account(email: str, base_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
    """Convenience function to get headers for account"""
    return get_account_identity_manager().get_headers_for_account(email, base_headers)
