# seat_processor.py - Seat data processing and auto-hold logic
import time
import threading
import logging
from PyQt5.QtCore import QTimer
from collections import defaultdict

logger = logging.getLogger("webook_pro")


class SeatProcessor:
    """Handles seat data processing and auto-hold logic"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def process_single_seat_update(self, seat_data):
        """
        Ultra-optimized seat update processing for <100ms auto-hold response.
        Eliminates all unnecessary operations and uses fastest possible checks.
        """
        # Basic validation
        if not seat_data or 'objectLabelOrUuid' not in seat_data:
            return

        seat_status = seat_data.get('status', 'free')

        # ALWAYS update seat status first (critical for preventing duplicate holds)
        self._update_seat_data(seat_data)

        # ULTRA-FAST PATH - Only proceed with auto-hold for free seats
        if (seat_status != 'free' or
            not self.main_window.ticket_type_tab.auto_hold_checkbox.isChecked()):
            return

        seat_id = seat_data['objectLabelOrUuid']

        # CRITICAL PATH - Atomic checks with minimal overhead

        # Skip if already being processed (fastest possible check)
        if seat_id in self.main_window.auto_held_seats:
            return

        # Skip if being transferred (use getattr for safety)
        if hasattr(self.main_window.token_system, 'transfer_in_progress_seats') and seat_id in self.main_window.token_system.transfer_in_progress_seats:
            return

        # Double-check seat status from our internal tracking (prevent race conditions)
        current_info = self.main_window.seat_id_map.get(seat_id)
        if current_info and current_info.get('status') != 'free':
            logger.debug(f"⚠️ Skipping {seat_id}: internal status is {current_info.get('status')}, not free")
            return

        # Fast max tickets check - cache the value to avoid repeated text parsing
        if not hasattr(self, '_cached_max_tickets') or time.time() - getattr(self, '_max_tickets_cache_time', 0) > 1.0:
            try:
                max_tickets_text = self.main_window.total_tickets_edit.text()
                self._cached_max_tickets = int(max_tickets_text) if max_tickets_text else 0
                self._max_tickets_cache_time = time.time()
            except ValueError:
                self._cached_max_tickets = 0
                self._max_tickets_cache_time = time.time()

        if self._cached_max_tickets > 0:
            # Ultra-fast seat count - avoid method calls
            current_held_count = len(self.main_window.token_system.get_all_seats()) if self.main_window.token_system else 0
            pending_holds = getattr(self.main_window, 'pending_seats', 0)

            if current_held_count + pending_holds >= self._cached_max_tickets:
                return

        # ULTRA-FAST TYPE CHECK - Cache selected types to avoid repeated calls
        if not hasattr(self, '_cached_selected_types') or time.time() - getattr(self, '_selected_types_cache_time', 0) > 0.5:
            hold_all = getattr(self.main_window.ticket_type_tab, 'hold_all_enabled', False)
            if hold_all:
                self._cached_selected_types = {'*'}  # Hold all mode
            else:
                self._cached_selected_types = set(self.main_window.ticket_type_tab.get_selected_types())
            self._selected_types_cache_time = time.time()

        # Skip type check if holding all seats
        if '*' not in self._cached_selected_types:
            # Ultra-fast type extraction - use partition for better performance
            seat_type = seat_id.partition('-')[0]
            if seat_type not in self._cached_selected_types:
                return

        # CRITICAL EXECUTION PATH - Minimal overhead

        # Atomic update with minimal lock time
        with self.main_window.data_lock:
            if seat_id in self.main_window.auto_held_seats:
                return

            # Use websocket timestamp for accurate performance measurement
            timestamp = seat_data.get('_websocket_timestamp', time.time())
            self.main_window.auto_held_seats[seat_id] = timestamp
            self.main_window.pending_seats += 1

        # Launch hold immediately without any delays
        logger.debug(f"🎯 Attempting to hold seat {seat_id} (websocket status: {seat_data.get('status')})")
        self._launch_ultra_fast_hold(seat_id)

    def _launch_ultra_fast_hold(self, seat_id):
        """Launch ultra-fast seat holding with auto_hold system - OPTIMIZED FOR BATCH HOLDING"""
        if hasattr(self.main_window, 'auto_hold_system') and self.main_window.auto_hold_system:
            try:
                # Check if we have multiple seats pending - use batch holding for efficiency
                pending_seats = []
                with self.main_window.data_lock:
                    # Collect seats that are pending but not yet submitted
                    for pending_seat, timestamp in list(self.main_window.auto_held_seats.items()):
                        if timestamp == self.main_window.auto_held_seats.get(seat_id):  # Same batch
                            pending_seats.append(pending_seat)

                # If we have multiple seats from the same batch, use batch holding
                if len(pending_seats) > 1:
                    start_time = time.perf_counter()
                    success = self.main_window.auto_hold_system.hold_multiple_seats(pending_seats)
                    response_time = (time.perf_counter() - start_time) * 1000

                    if success:
                        self.main_window.log(f"🚀 BATCH HOLD: {len(pending_seats)} seats in {response_time:.1f}ms")
                    else:
                        self.main_window.log(f"⚠️ Batch hold failed, trying individual holds")
                        # Fall back to individual holds
                        for seat in pending_seats:
                            self.main_window.auto_hold_system.hold_seat(seat)
                else:
                    # Single seat - use individual hold
                    start_time = time.perf_counter()
                    success = self.main_window.auto_hold_system.hold_seat(seat_id)
                    response_time = (time.perf_counter() - start_time) * 1000

                    if success:
                        self.main_window.log(f"🚀 Ultra-fast hold: {seat_id} in {response_time:.1f}ms")
                    else:
                        self.main_window.log(f"⚠️ Ultra-fast hold failed for {seat_id}")
                        self._try_fallback_hold(seat_id)

            except Exception as e:
                self.main_window.log(f"❌ Error in ultra-fast hold for seat {seat_id}: {str(e)}")
                self._try_fallback_hold(seat_id)
        else:
            # Fallback to old async method if new system not available
            self.main_window.log(f"⚠️ Auto-hold system not available, using fallback for {seat_id}")
            self._try_fallback_hold(seat_id)

    def _try_fallback_hold(self, seat_id):
        """Fallback async hold method"""
        def hold_async():
            try:
                result = self.main_window.run_async(self._hold_seat_async(seat_id))
                if result:
                    # Schedule UI update on main thread
                    QTimer.singleShot(0, lambda: self.main_window.on_async_seat_held(seat_id, result))
                else:
                    QTimer.singleShot(0, lambda: self.main_window.log(f"❌ Failed to hold seat {seat_id}"))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.main_window.log(f"❌ Error holding seat {seat_id}: {str(e)}"))

        # Run in background thread
        threading.Thread(target=hold_async, daemon=True).start()
        return True

    async def _hold_seat_async(self, seat_id: str):
        """Async method to hold a seat - fallback when auto-hold is not available"""
        try:
            if not hasattr(self.main_window, 'event_key') or not self.main_window.event_key:
                logger.error("No event key available for holding seat")
                return None

            # Try to use the token system to hold the seat directly
            if self.main_window.token_system:
                # Queue a hold operation through the token system
                success = self.main_window.token_system.hold_seat(seat_id)
                if success:
                    # Return a placeholder token ID since the actual token is managed internally
                    return "token_managed_internally"
                else:
                    logger.error(f"Token system failed to hold seat {seat_id}")
                    return None
            else:
                logger.error("No token system available for fallback hold")
                return None

        except Exception as e:
            logger.error(f"Error in async seat hold: {str(e)}")
            return None

    def _process_non_free_seat(self, seat_data):
        """Process non-free seats with lower priority"""
        # Schedule this to run after critical operations
        QTimer.singleShot(50, lambda: self._update_seat_data(seat_data))

    def _process_non_auto_seat(self, seat_data):
        """Process free seats that we're not auto-holding"""
        # Schedule this to run after critical operations
        QTimer.singleShot(10, lambda: self._update_seat_data(seat_data))

    def _update_seat_data(self, seat_data):
        """Update internal seat data structures (critical for preventing duplicate holds)"""
        try:
            with self.main_window.data_lock:
                seat_id = seat_data['objectLabelOrUuid']
                new_status = seat_data.get('status', 'free').lower()

                # Extract seat type
                seat_parts = seat_id.split('-')
                seat_type = seat_parts[0].strip() if seat_parts else seat_id

                # Get existing seat info
                current_info = self.main_window.seat_id_map.get(seat_id)

                if current_info:
                    # Seat already tracked - update status
                    previous_status = current_info['status']
                    if new_status != previous_status:
                        # Log status changes for debugging
                        logger.debug(f"🔄 Seat {seat_id}: {previous_status} → {new_status}")

                        # Remove from old status dictionary
                        if seat_type in self.main_window.tickets_info and previous_status in self.main_window.tickets_info[seat_type]:
                            self.main_window.tickets_info[seat_type][previous_status].pop(seat_id, None)

                        # Add to new status dictionary
                        if seat_type not in self.main_window.tickets_info:
                            self.main_window.tickets_info[seat_type] = {}
                        if new_status not in self.main_window.tickets_info[seat_type]:
                            self.main_window.tickets_info[seat_type][new_status] = {}
                        self.main_window.tickets_info[seat_type][new_status][seat_id] = seat_data
                        self.main_window.seat_id_map[seat_id]['status'] = new_status
                else:
                    # New seat - add to tracking
                    self.main_window.seat_id_map[seat_id] = {
                        'type': seat_type,
                        'status': new_status
                    }

                    # Add to status dictionary
                    if seat_type not in self.main_window.tickets_info:
                        self.main_window.tickets_info[seat_type] = {}
                    if new_status not in self.main_window.tickets_info[seat_type]:
                        self.main_window.tickets_info[seat_type][new_status] = {}
                    self.main_window.tickets_info[seat_type][new_status][seat_id] = seat_data

            # Update UI for this seat type
            self.main_window.ticket_type_tab.update_type_row(seat_type)
        except Exception as e:
            logger.error(f"Error updating seat data: {str(e)}")

    def reset_auto_held_seats(self):
        """
        Reset the auto_held_seats dictionary and update the window title.
        This allows the system to try holding seats that previously failed.
        """
        with self.main_window.data_lock:
            self.main_window.auto_held_seats = {}
            self.main_window.pending_seats = 0

        # Reset window title
        current_title = self.main_window.windowTitle()
        if '(' in current_title:
            base_title = current_title.split('(')[0].strip()
            held_count = 0
            if self.main_window.token_system:
                held_count = len(self.main_window.token_system.get_all_seats())

            if held_count > 0:
                self.main_window.setWindowTitle(f"{base_title} (Auto-held: {held_count})")
            else:
                self.main_window.setWindowTitle(base_title)

        self.main_window.log("🔄 Auto-hold tracking reset - previously failed seats will be eligible for auto-hold")

        # Update UI
        self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def on_async_seat_held(self, seat_number, token):
        """
        Handler for seats held by the async hold system.
        """
        try:
            with self.main_window.data_lock:
                # Reduce pending count immediately
                self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)

                # Remove from auto_held_seats to allow holding other seats
                self.main_window.auto_held_seats.pop(seat_number, None)

            self.main_window.log(f"✅ Held seat {seat_number}")

            # Check if max tickets reached
            max_tickets = 0
            try:
                max_tickets_text = self.main_window.total_tickets_edit.text()
                if max_tickets_text:
                    max_tickets = int(max_tickets_text)
            except ValueError:
                pass

            # Only check max tickets if there's a limit
            if max_tickets > 0:
                # Get seat count from token system directly
                if self.main_window.token_system:
                    held_seats = len(self.main_window.token_system.get_all_seats())
                    if held_seats > max_tickets:
                        # Release excess seat in background
                        self._release_excess_seat(seat_number, token)
                        return

            # Register with TokenManagementSystem
            if self.main_window.token_system:
                self.main_window.token_system.register_held_seat(seat_number, token)

                # Trigger UI update asynchronously
                QTimer.singleShot(50, lambda: self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True))
                QTimer.singleShot(75, self.main_window._update_window_title)

        except Exception as e:
            # Silent exception handling on critical path
            pass

    def _release_excess_seat(self, seat_number, token):
        """Release excess seat using async method"""
        self.main_window.release_seat_sync(seat_number, token)
        self.main_window.log(f"Releasing excess seat: {seat_number}")

    def on_auto_hold_success(self, seat_id: str, token: str):
        """Handle successful seat hold from auto-hold system"""
        with self.main_window.data_lock:
            # Remove from auto_held_seats to allow holding other seats
            self.main_window.auto_held_seats.pop(seat_id, None)
            self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)

            # Track batch booking results
            if hasattr(self.main_window, '_batch_booking_results'):
                self.main_window._batch_booking_results['success'] += 1

        # Register with TokenManagementSystem if available (with error handling)
        if self.main_window.token_system:
            try:
                self.main_window.token_system.register_held_seat(seat_id, token)
            except Exception as e:
                # Don't let token registration errors crash the batch operation
                self.main_window.log(f"⚠️ Token registration error for {seat_id}: {str(e)}")

        # Don't log individual successes during batch operations to reduce spam
        # The batch completion will show the final results

    def on_auto_hold_failure(self, seat_id: str, error: str):
        """Handle failed seat hold from auto-hold system"""
        with self.main_window.data_lock:
            # Remove from auto_held_seats to allow retrying
            self.main_window.auto_held_seats.pop(seat_id, None)
            self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)

            # Track batch booking results
            if hasattr(self.main_window, '_batch_booking_results'):
                self.main_window._batch_booking_results['failed'] += 1

        # Don't log individual failures during batch operations to reduce spam
        # Don't trigger individual fallback attempts - batch retry will handle this
