# system_initializer.py - System initialization methods
import logging
from PyQt5.QtCore import QTimer, QTime
from chart_token_manager import get_chart_token
from manager.proxy_manager import get_global_proxy_manager
from token_management_system import TokenManagementSystem
from threads.event_websocket_thread import WebSocket<PERSON>anager
from helper import get_event_seatsio_info, get_object_statuses, group_tickets_by_type_and_status

logger = logging.getLogger("webook_pro")


class SystemInitializer:
    """Handles system initialization for the main window"""
    
    def __init__(self, main_window):
        self.main_window = main_window
    
    def initialize_chart_token(self):
        """Initialize chart token when the application starts"""
        # Force an initial fetch of the chart token
        chart_token = get_chart_token(force_refresh=True, proxy=self.main_window.get_proxy_string())
        if chart_token:
            self.main_window.log(f"🔑 Chart token initialized: {chart_token[:8]}...")
        else:
            self.main_window.log("⚠️ Failed to initialize chart token")

    def initialize_token_management_system(self):
        """Initialize the centralized token management system"""
        # Store event key for other components
        self.main_window.event_key = self.main_window.webook_data['data']['seats_io']['event_key']
        self.main_window.channel_keys = self.main_window.webook_data['data']['channel_keys']

        # Create the token management system
        self.main_window.token_system = TokenManagementSystem(
            event_key=self.main_window.event_key,
            chart_key=self.main_window.webook_data['data']['seats_io']['chart_key'],
            channel_keys=self.main_window.channel_keys,
            team_id=self.main_window.team_combo.currentData() or None,  # Allow None for no team
            proxies=[self.main_window.get_proxy_string()] if self.main_window.get_proxy_string() else []
        )

        self.main_window.token_system.log_signal.connect(self.main_window.log)
        self.main_window.token_system.seat_held_signal.connect(self.main_window.on_seat_held)
        self.main_window.token_system.seat_released_signal.connect(self.main_window.on_seat_released)
        self.main_window.token_system.seat_transferred_signal.connect(self.main_window.on_seat_transferred)
        self.main_window.token_system.seat_expired_signal.connect(self.main_window.on_seats_expired)
        self.main_window.token_system.ui_update_signal.connect(self.main_window.held_seats_tab.auto_refresh_held_seats)
        self.main_window.token_system.token_renewed_signal.connect(self.main_window.on_token_renewed)

        self.main_window.log("TokenManagementSystem initialized")

    def initialize_proxy_system(self):
        """Initialize the proxy system with current settings"""
        # Initialize the global proxy manager with current settings
        proxy_manager = get_global_proxy_manager(self.main_window.proxy_config)

        # Log proxy configuration
        if self.main_window.proxy_config.get("enabled", False):
            if self.main_window.proxy_config.get("mode", "single") == "single":
                domain = self.main_window.proxy_config.get("domain", "")
                port = self.main_window.proxy_config.get("port", "")
                user = self.main_window.proxy_config.get("username", "")
                rotating = self.main_window.proxy_config.get("use_rotating", True)

                if rotating:
                    self.main_window.log(f"Proxy enabled: {domain}:{port} with rotation (User: {user})")
                else:
                    self.main_window.log(f"Proxy enabled: {domain}:{port} without rotation (User: {user})")
            else:
                proxy_count = len(self.main_window.proxy_config.get("proxy_list", []))
                rotation = self.main_window.proxy_config.get("local_rotation", True)
                rotation_count = self.main_window.proxy_config.get("rotation_count", 50)

                if rotation:
                    self.main_window.log(f"Local proxy rotation enabled with {proxy_count} proxies. Rotating every {rotation_count} requests.")
                else:
                    self.main_window.log(f"Local proxy list enabled with {proxy_count} proxies. No automatic rotation.")

    def start_simple_holding_system(self):
        """Initialize the SimpleHoldManager for seat holding"""
        # Get event_id if available
        event_id = None
        if hasattr(self.main_window, 'webook_data') and self.main_window.webook_data:
            event_id = self.main_window.webook_data.get("data", {}).get("_id")

        # Initialize new auto-hold system
        try:
            from auto_hold import initialize_auto_hold
            self.main_window.auto_hold_system = initialize_auto_hold(
                event_key=self.main_window.event_key,
                channel_keys=self.main_window.channel_keys,
                team_id=self.main_window.team_combo.currentData() or None,
                proxy=self.main_window.get_proxy_string()
            )

            # Set up callbacks for success and failure
            self.main_window.auto_hold_system.set_callbacks(
                success_callback=self.main_window.seat_processor.on_auto_hold_success,
                failure_callback=self.main_window.seat_processor.on_auto_hold_failure
            )

            # Initialize batch booking tracking
            self.main_window._batch_booking_results = {'success': 0, 'failed': 0, 'total': 0}

            self.main_window.log("🚀 Auto-hold system initialized with callbacks")
        except Exception as e:
            self.main_window.log(f"⚠️ Failed to initialize auto-hold system: {e}")
            self.main_window.auto_hold_system = None

        self._start_token_renewal_timer()

    def _start_token_renewal_timer(self):
        """Start a timer to periodically check if tokens need renewal"""
        self.main_window.renewal_timer = QTimer()
        self.main_window.renewal_timer.timeout.connect(self._check_and_renew_token)
        self.main_window.renewal_timer.start(60 * 1000)  # Check every minute

    def _check_and_renew_token(self):
        """Check token status for new auto-hold system"""
        if hasattr(self.main_window, 'fast_token_manager') and self.main_window.fast_token_manager:
            # New system handles token renewal automatically
            status = self.main_window.fast_token_manager.get_current_status()
            if not status['ready'] and status['target_tickets'] > 0:
                logger.info(f"🔄 Token status: {status['available_tokens']}/{status['target_tokens']}")

    def initialize_websocket(self, data):
        """Initialize WebSocket connection for real-time seat status updates"""
        if self.main_window.websocket_manager and self.main_window.websocket_manager.thread.isRunning():
            self.main_window.websocket_manager.stop()

        # Handle season_event_key correctly
        season_event_key = data['event_info'].get('season_info', {})
        if season_event_key:
            season_event_key = season_event_key.get('topLevelSeasonKey')
        event_key = season_event_key or data["event_key"]

        self.main_window.websocket_manager = WebSocketManager(
            event_key,
            data["chart_key"],
            parent=None
        )
        self.main_window.websocket_manager.seat_data_updated.connect(
            self.main_window.seat_processor.process_single_seat_update
        )
        self.main_window.websocket_manager.error_signal.connect(self.main_window.on_refresh_error)
        self.main_window.websocket_manager.connected_signal.connect(self.main_window.on_websocket_connected)
        self.main_window.websocket_manager.start()

    def initialize_channel_key_refresh_timer(self):
        """Initialize timer for checking channel key updates"""
        self.main_window.channel_key_timer = QTimer()
        self.main_window.channel_key_timer.timeout.connect(self.check_channel_key_updates)
        
        # Calculate time until next half-hour mark
        current_time = QTime.currentTime()
        current_minute = current_time.minute()
        current_second = current_time.second()
        
        # Calculate milliseconds until next check time (start of hour or half-hour)
        if current_minute < 30:
            # Time until 30-minute mark
            minutes_to_wait = 30 - current_minute
        else:
            # Time until next hour
            minutes_to_wait = 60 - current_minute
        
        # Subtract seconds already elapsed in the current minute
        seconds_to_wait = minutes_to_wait * 60 - current_second
        msecs_to_wait = seconds_to_wait * 1000
        
        # Start one-shot timer for first alignment to hour/half-hour
        QTimer.singleShot(msecs_to_wait, self.start_periodic_channel_key_check)
        
        self.main_window.log("🔄 Channel key refresh timer initialized. First check in " + 
                f"{minutes_to_wait} minutes and {current_second} seconds")

    def start_periodic_channel_key_check(self):
        """Start the periodic channel key check timer"""
        # Now we're aligned to hour/half-hour, start the regular 30-minute timer
        self.main_window.channel_key_timer.start(30 * 60 * 1000)  # 30 minutes in milliseconds
        
        # Also perform the first check immediately
        self.check_channel_key_updates()

    def check_channel_key_updates(self):
        """Check for updates to channel keys and reload if necessary"""
        if not hasattr(self.main_window, 'webook_data') or not self.main_window.webook_data:
            self.main_window.log("⚠️ No event loaded, skipping channel key check")
            return
        
        try:
            # Get current time for logging
            current_time = QTime.currentTime()
            time_str = current_time.toString("HH:mm:ss")
            self.main_window.log(f"🔍 {time_str} - Scheduled channel key check running...")
            
            # Use the enhanced reload_event_data method
            event_key = self.main_window.webook_data['data']['seats_io']['event_key']
            success = self.main_window.reload_event_data(event_key)
            
            if success:
                self.main_window.log(f"✓ {time_str} - Channel key check completed successfully")
                
                # Update the window title to reflect any changes in held seats
                self.main_window._update_window_title()
                
                # Force refresh of held seats tab to ensure accurate data
                if hasattr(self.main_window, 'held_seats_tab'):
                    self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
                
                # Update ticket type selection tab to ensure accurate data
                if hasattr(self.main_window, 'ticket_type_tab'):
                    self.main_window.ticket_type_tab.update_tickets_info(self.main_window.tickets_info)
            else:
                self.main_window.log(f"⚠️ {time_str} - Channel key check failed, will retry at next scheduled time")
                
        except Exception as e:
            self.main_window.log(f"❌ Error in scheduled channel key check: {str(e)}")
            logger.error(f"Error in scheduled channel key check: {str(e)}", exc_info=True)

    async def reload_event_data_and_reconnect(self):
        """Reload event data and reconnect WebSocket when connection is lost"""
        # Only reload if we have event data already
        if not hasattr(self.main_window, 'webook_data') or not self.main_window.webook_data:
            self.main_window.log("[WebSocket] No event data to reload")
            return False

        event_key = self.main_window.webook_data['data']['seats_io']['event_key']
        self.main_window.log(f"[WebSocket] Reloading event data for {event_key} after disconnection...")

        try:
            # Reload webook data using WebookClient
            from webook_client import WebookClient
            client = WebookClient(proxy=self.main_window.get_proxy_string())
            try:
                self.main_window.webook_data = client.get_event_info(event_key=event_key)
                if not self.main_window.webook_data:
                    self.main_window.log(f'[WebSocket] Failed to reload event data')
                    return False

                data = self.main_window.webook_data["data"]["seats_io"]
                data['event_info'] = await get_event_seatsio_info(data, proxy=self.main_window.get_proxy_string())
            finally:
                client.close()

            # Reload seats data
            chart_key = data["chart_key"]
            seats = await get_object_statuses(data["event_key"], chart_key, proxy=self.main_window.get_proxy_string())
            self.main_window.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)

            # Reinitialize seat map
            for ttype, statuses in self.main_window.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.main_window.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }

            # Reinitialize WebSocket with fresh connection
            self.initialize_websocket(data)

            # Refresh UI
            self.main_window.ui_components.update_initial_ui()

            self.main_window.log("[WebSocket] Event data reloaded and WebSocket reconnected successfully")
            return True

        except Exception as e:
            self.main_window.log(f"[WebSocket] Error reloading event data: {str(e)}")
            logger.exception("Error reloading event data", exc_info=True)
            return False
