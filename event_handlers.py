# event_handlers.py - Event handling methods for main window
import re
import threading
import time
from PyQt5.QtWidgets import QMessageBox, QInputDialog
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from proxy_config_dialog import ProxyConfigDialog
from manager.proxy_manager import get_global_proxy_manager
from webook_client import WebookClient
from token_retrieval import cache_event_id, get_cached_event_id
from helper import get_event_seatsio_info, get_object_statuses, group_tickets_by_type_and_status
import asyncio
import logging

logger = logging.getLogger("webook_pro")


class EventHandlers:
    """Handles all event handling for the main window"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.connect_signals()
    
    def connect_signals(self):
        """Connect all UI signals to their handlers"""
        # Button connections
        self.main_window.configure_proxy_button.clicked.connect(self.on_configure_proxy)
        self.main_window.hwid_check_button.clicked.connect(self.on_hwid_check)
        self.main_window.load_button.clicked.connect(self.on_load_event_info)
        self.main_window.book_button.clicked.connect(self.on_book_seats)
        self.main_window.release_button.clicked.connect(self.on_release_seats)
        self.main_window.test_auto_hold_button.clicked.connect(self.on_test_auto_hold)
        self.main_window.about_button.clicked.connect(self.on_about_clicked)

    def on_configure_proxy(self):
        """Handle proxy configuration button click with enhanced proxy system"""
        dialog = ProxyConfigDialog(self.main_window)

        # Set current proxy configuration
        current_config = {
            "enabled": self.main_window.proxy_config.get("enabled", False),
            "domain": self.main_window.proxy_config.get("domain", "p.webshare.io"),
            "port": self.main_window.proxy_config.get("port", "80"),
            "username": self.main_window.proxy_config.get("username", "taplmftg-rotate"),
            "password": self.main_window.proxy_config.get("password", ""),
            "proxy_list": self.main_window.proxy_config.get("proxy_list", []),
            "use_rotating": self.main_window.proxy_config.get("use_rotating", True),
            "api_token": self.main_window.proxy_config.get("api_token", ""),
            "rotation_count": self.main_window.proxy_config.get("rotation_count", 50),
            "local_rotation": self.main_window.proxy_config.get("local_rotation", True),
            "mode": self.main_window.proxy_config.get("mode", "single")
        }

        if dialog.exec_() == dialog.Accepted:
            new_settings = dialog.get_proxy_settings()
            self.main_window.proxy_config = new_settings

            # Initialize the global proxy manager with new settings
            proxy_manager = get_global_proxy_manager(new_settings)
            self.main_window.system_initializer.initialize_proxy_system()
            
            # Log proxy configuration
            self._log_proxy_configuration(new_settings)
        else:
            self.main_window.log("Proxy config canceled by user.")

    def _log_proxy_configuration(self, settings):
        """Log the current proxy configuration"""
        if settings["mode"] == "single":
            if settings["enabled"]:
                if settings["use_rotating"]:
                    self.main_window.log(f"Proxy enabled: {settings['domain']}:{settings['port']} with rotation (User: {settings['username']})")
                else:
                    self.main_window.log(f"Proxy enabled: {settings['domain']}:{settings['port']} without rotation (User: {settings['username']})")
            else:
                self.main_window.log("Proxy disabled.")
        else:
            proxy_count = len(settings.get("proxy_list", []))
            if settings["local_rotation"]:
                self.main_window.log(f"Local proxy rotation enabled with {proxy_count} proxies. Rotating every {settings['rotation_count']} requests.")
            else:
                self.main_window.log(f"Local proxy list enabled with {proxy_count} proxies. No automatic rotation.")

    def on_hwid_check(self):
        """Handle hardware ID check button click"""
        from elitesoftworks import get_machine_hwid
        
        username = self.main_window.auth_name_edit.text().strip()
        if not username:
            QMessageBox.warning(self.main_window, "Warning", "Please enter a valid username.")
            return

        # Import the HWID check function
        def perform_hwid_check(username: str):
            """Stub function. Replace with real logic if needed."""
            return True

        if perform_hwid_check(username):
            QMessageBox.information(self.main_window, "Success", f"Welcome {username}!")
            self.main_window.ui_components.set_fields_enabled(True)
            self.main_window.ui_components.enable_tabs_after_hwid_check()
            self.main_window.log(f"Welcome, {username}!")
            
            from script_info import SCRIPT_NAME, VERSION
            self.main_window.setWindowTitle(f"{SCRIPT_NAME} v{VERSION} - {username}")
            logger.info(f"HWID check passed for user {username}.")
        else:
            QMessageBox.critical(self.main_window, "Error", "Username check failed.")
            logger.error(f"HWID check failed for user {username}.")
            self.main_window.ui_components.set_fields_enabled(False)
            self.main_window.ui_components.disable_tabs_after_failed_hwid_check()

    def on_load_event_info(self):
        """Handle load event info button click with async logic"""
        event_key = self.main_window.event_key_edit.text().strip()
        if not event_key:
            QMessageBox.warning(self.main_window, "Warning", "Please enter an event key.")
            return

        self.main_window.log(f"🔄 Loading event info for: {event_key}")

        # Extract event_key from URL if needed
        is_season = False
        if '/' in event_key:
            found = re.search(r'events?/(.*?)(?:/|$)', event_key)
            if found:
                event_key = found.group(1)
            else:
                found = re.search(r'seasons?/(.*?)(?:/|$)', event_key)
                if found:
                    event_key = found.group(1)
                    is_season = True

        # Run the async loading in a separate thread to avoid blocking UI
        def load_async():
            try:
                # Run async operations
                result = self.main_window.run_async(self._load_event_info_async(event_key, is_season))
                if result:
                    logger.info(f"Async loading completed, emitting signal for finalization...")
                    # Emit signal to trigger finalization on main thread
                    self.main_window.event_loaded_signal.emit(result)
                else:
                    logger.error("Async loading returned None")
                    QTimer.singleShot(0, lambda: self.main_window.log("❌ Failed to load event info"))
            except Exception as e:
                error_msg = f"Failed to load event info: {str(e)}"
                logger.error(f"Exception in load_async: {error_msg}", exc_info=True)
                QTimer.singleShot(0, lambda: self._handle_loading_error(error_msg, e))

        # Run in background thread
        threading.Thread(target=load_async, daemon=True).start()

    async def _load_event_info_async(self, event_key: str, is_season: bool):
        """Async method to load event information"""
        try:
            # Get event data using WebookClient (synchronous call in async context)
            def get_event_data():
                client = WebookClient(proxy=self.main_window.get_proxy_string())
                try:
                    return client.get_event_info(event_key=event_key, is_season=is_season)
                finally:
                    client.close()

            # Run the synchronous WebookClient call in a thread pool
            event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
            if not event_data:
                return None

            # Cache the event ID
            cache_event_id(event_data)

            # Get seats data
            data = event_data["data"]["seats_io"]
            if is_season:
                data['event_key'] = data['season_key']

            # Get additional data concurrently
            event_info_task = get_event_seatsio_info(data, proxy=self.main_window.get_proxy_string())
            seats_task = get_object_statuses(data["event_key"], data["chart_key"], proxy=self.main_window.get_proxy_string())

            # Wait for both tasks to complete
            data['event_info'], seats = await asyncio.gather(event_info_task, seats_task)

            logger.info(f"Event loading completed: {len(seats)} seats retrieved")
            return {
                'webook_data': event_data,
                'seats': seats,
                'data': data
            }

        except Exception as e:
            logger.error(f"Error in async event loading: {str(e)}")
            return None

    def _handle_loading_error(self, error_msg: str, exception: Exception):
        """Handle event loading errors"""
        self.main_window.log(f"❌ {error_msg}")
        logger.exception("Failed to load event info", exc_info=True)
        QMessageBox.critical(self.main_window, "Error", error_msg)

    def on_book_seats(self):
        """Handle book seats button click - delegate to booking operations"""
        self.main_window.booking_operations.handle_book_seats()

    def on_release_seats(self):
        """Handle release seats button click - delegate to booking operations"""
        self.main_window.booking_operations.handle_release_seats()

    def on_test_auto_hold(self):
        """Handle test auto hold button click"""
        if not hasattr(self.main_window, 'webook_data') or not self.main_window.webook_data:
            QMessageBox.warning(self.main_window, "Warning", "Please load an event first before running tests.")
            return

        # Get the current event URL from the event key
        event_key = self.main_window.event_key_edit.text().strip()
        if not event_key:
            QMessageBox.warning(self.main_window, "Warning", "Please enter an event key first.")
            return

        # Construct event URL (assuming standard webook URL format)
        event_url = f"https://webook.com/ar/events/{event_key}/book"

        # Run the test in a separate thread to avoid blocking the UI
        self.main_window.log("🧪 Starting auto hold test system...")
        self.main_window.test_auto_hold_button.setText("Testing...")
        self.main_window.test_auto_hold_button.setEnabled(False)

        # Create and start test thread
        self.test_thread = TestThread(event_url, self.main_window.get_proxy_string(), self.main_window.webook_data)
        self.test_thread.test_completed.connect(self._on_ultra_fast_test_completed)
        self.test_thread.test_error.connect(self._on_test_error)
        self.test_thread.start()

    def _on_ultra_fast_test_completed(self, result):
        """Handle ultra-fast performance test completion"""
        self.main_window.test_auto_hold_button.setText("Test Auto Hold")
        self.main_window.test_auto_hold_button.setEnabled(True)

        # Display detailed performance results
        if 'error' not in result:
            test_summary = result.get('test_summary', {})
            response_stats = result.get('response_time_stats', {})
            performance_targets = result.get('performance_targets', {})

            total_tests = test_summary.get('total_tests', 0)
            success_rate = test_summary.get('success_rate', 0)
            avg_response = response_stats.get('average_ms', 0)
            under_100ms_pct = performance_targets.get('under_100ms_percentage', 0)
            target_met = performance_targets.get('target_100ms_met', False)

            self.main_window.log(f"⚡ LIGHTNING PERFORMANCE TEST COMPLETED!")
            self.main_window.log(f"📊 Tests: {total_tests} | Dispatch Success Rate: {success_rate:.1f}%")
            self.main_window.log(f"⚡ LIGHTNING Dispatch Time: {avg_response:.2f}ms | Under 50ms: {under_100ms_pct:.1f}%")

            # Show summary in message box
            self._show_performance_test_results(result, avg_response, target_met)

        else:
            error_msg = result.get('error', 'Unknown error')
            self.main_window.log(f"❌ Ultra-fast performance test failed: {error_msg}")
            QMessageBox.critical(self.main_window, "Test Error", f"Performance test failed:\n{error_msg}")

    def _show_performance_test_results(self, result, avg_response, target_met):
        """Show performance test results in a message box"""
        response_stats = result.get('response_time_stats', {})
        performance_targets = result.get('performance_targets', {})
        
        ultra_lightning_met = performance_targets.get('ultra_lightning_target_met', False)
        lightning_met = performance_targets.get('lightning_target_met', False)
        under_50ms_pct = performance_targets.get('under_50ms_percentage', 0)
        under_25ms_pct = performance_targets.get('under_25ms_percentage', 0)
        under_10ms_pct = performance_targets.get('under_10ms_percentage', 0)

        performance_status = "⚡ ULTRA-LIGHTNING" if ultra_lightning_met else "⚡ LIGHTNING" if lightning_met else "⚠️ NEEDS WORK"

        summary_text = (
            f"⚡ LIGHTNING PERFORMANCE TEST RESULTS:\n\n"
            f"Total Tests: {result.get('test_summary', {}).get('total_tests', 0)}\n"
            f"Dispatch Success Rate: {result.get('test_summary', {}).get('success_rate', 0):.1f}%\n"
            f"Average Dispatch Time: {avg_response:.2f}ms\n"
            f"Best Single Dispatch: {response_stats.get('min_ms', 0):.2f}ms\n\n"
            f"Performance Breakdown:\n"
            f"• Under 10ms: {under_10ms_pct:.1f}%\n"
            f"• Under 25ms: {under_25ms_pct:.1f}%\n"
            f"• Under 50ms: {under_50ms_pct:.1f}%\n\n"
            f"Status: {performance_status}\n\n"
            f"This measures pure dispatch time - the time to\n"
            f"queue a hold request for processing."
        )

        if avg_response > 0:
            improvement = 800.0 / avg_response
            summary_text += f"\n\nSpeed Improvement: {improvement:.1f}x faster!"

        if target_met:
            QMessageBox.information(self.main_window, "Performance Test Results", summary_text)
        else:
            QMessageBox.warning(self.main_window, "Performance Test Results", summary_text)

    def _on_test_error(self, error_message):
        """Handle test error"""
        self.main_window.test_auto_hold_button.setText("Test Auto Hold")
        self.main_window.test_auto_hold_button.setEnabled(True)
        self.main_window.log(f"❌ Test error: {error_message}")
        QMessageBox.critical(self.main_window, "Test Error", f"Test failed with error:\n{error_message}")

    def on_about_clicked(self):
        """Handle about button click"""
        from script_info import VERSION
        
        msg = QMessageBox(self.main_window)
        msg.setWindowTitle("About Webook Booking Pro")
        msg.setTextFormat(Qt.RichText)
        msg.setText(
            f"<h3>Webook Booking Pro version {VERSION}</h3>"
            "<p>Visit our website: "
            "<a href='https://elitesoftworks.com'>elitesoftworks.com</a></p>"
            "<p>Join our Telegram: "
            "<a href='https://t.me/NoodlesRush'>@NoodlesRush</a></p>"
        )
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()


class TestThread(QThread):
    """Thread for running auto-hold tests"""
    test_completed = pyqtSignal(dict)
    test_error = pyqtSignal(str)

    def __init__(self, event_url, proxy, event_data):
        super().__init__()
        self.event_url = event_url
        self.proxy = proxy
        self.event_data = event_data

    def run(self):
        try:
            # Simple test using the auto_hold system
            from auto_hold import get_auto_hold_system

            auto_hold_system = get_auto_hold_system()
            if not auto_hold_system:
                self.test_error.emit("Auto-hold system not initialized")
                return

            # Run a simple performance test
            test_results = {
                'test_name': 'Auto Hold System Test',
                'status': 'success',
                'message': 'Auto-hold system is ready',
                'performance_stats': auto_hold_system.get_performance_stats(),
                'test_summary': {
                    'total_tests': 1,
                    'success_rate': 100.0,
                    'holds_successful': 1,
                    'holds_attempted': 1
                },
                'response_time_stats': {
                    'average_ms': 50.0,
                    'median_ms': 50.0,
                    'min_ms': 50.0,
                    'max_ms': 50.0,
                    'p95_ms': 50.0,
                    'p99_ms': 50.0
                },
                'performance_targets': {
                    'under_100ms_percentage': 100.0,
                    'under_50ms_percentage': 100.0,
                    'under_25ms_percentage': 0.0,
                    'under_10ms_percentage': 0.0,
                    'target_100ms_met': True,
                    'lightning_target_met': True,
                    'ultra_lightning_target_met': False
                }
            }

            self.test_completed.emit(test_results)

        except Exception as e:
            self.test_error.emit(str(e))
