#!/usr/bin/env python3
"""
Debug Auto-Hold Callbacks - Simple test to see what's happening
"""

import logging
import time
import threading
from auto_hold import AutoHoldSystem

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("debug_callbacks")

def debug_auto_hold():
    """Debug the auto-hold system step by step"""
    
    # Track callback results
    success_calls = []
    failure_calls = []
    
    def success_callback(seat_id: str, token: str):
        success_calls.append((seat_id, token))
        logger.info(f"🎉 SUCCESS CALLBACK TRIGGERED: {seat_id} with token {token[:8]}...")
        
    def failure_callback(seat_id: str, error: str):
        failure_calls.append((seat_id, error))
        logger.info(f"💥 FAILURE CALLBACK TRIGGERED: {seat_id} - {error}")
    
    try:
        logger.info("🔧 Creating AutoHoldSystem directly...")
        
        # Create auto-hold system directly (not through initialize_auto_hold)
        auto_hold_system = AutoHoldSystem(
            event_key="debug-event",
            channel_keys=["debug-channel"],
            team_id=None,
            proxy=None
        )
        
        logger.info("🔧 Setting callbacks...")
        auto_hold_system.set_callbacks(
            success_callback=success_callback,
            failure_callback=failure_callback
        )
        
        logger.info(f"🔧 Callbacks set: success={auto_hold_system.success_callback is not None}, failure={auto_hold_system.failure_callback is not None}")
        
        # Check token status
        status = auto_hold_system.get_token_status()
        logger.info(f"🔧 Token status: {status}")
        
        # Try to get a token manually
        logger.info("🔧 Trying to get token manually...")
        token = auto_hold_system.token_pool.get_token()
        logger.info(f"🔧 Manual token result: {token}")
        
        # Now try to hold a seat
        logger.info("🔧 Attempting to hold seat...")
        result = auto_hold_system.hold_seat("debug-seat-1")
        logger.info(f"🔧 Hold seat returned: {result}")
        
        # Wait for async operation
        logger.info("🔧 Waiting for async operation...")
        time.sleep(3)
        
        # Check results
        logger.info(f"🔧 Success callbacks: {len(success_calls)}")
        logger.info(f"🔧 Failure callbacks: {len(failure_calls)}")
        
        if len(failure_calls) > 0:
            logger.info("✅ Failure callback working!")
            for seat_id, error in failure_calls:
                logger.info(f"   - {seat_id}: {error}")
        else:
            logger.error("❌ No failure callback triggered!")
        
        # Check performance stats
        stats = auto_hold_system.get_performance_stats()
        logger.info(f"🔧 Performance stats: {stats}")
        
        # Check if there are any active operations
        logger.info(f"🔧 Active operations: {len(auto_hold_system.performance.active_operations)}")
        
        return len(failure_calls) > 0 or len(success_calls) > 0
        
    except Exception as e:
        logger.error(f"❌ Debug failed: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = debug_auto_hold()
    if success:
        print("\n🎉 Callbacks are working!")
    else:
        print("\n❌ Callbacks are not working!")
