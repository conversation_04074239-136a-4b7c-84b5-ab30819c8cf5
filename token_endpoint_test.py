#!/usr/bin/env python3
"""
Token Endpoint Test
Tests the specific TOKEN_ENDPOINT that was highlighted to see if it's working correctly
"""

import asyncio
import json
import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_token_endpoint():
    """Test the TOKEN_ENDPOINT from chart_token_manager.py"""
    
    logger.info("🧪 Testing TOKEN_ENDPOINT: https://34.102.211.90/chart.js")
    
    try:
        from helper import async_make_request
        from account_token_manager import get_account_tokens
        from proxy_manager import get_proxy_string
    except ImportError as e:
        logger.error(f"❌ Failed to import required modules: {e}")
        return
    
    # Get system configuration
    proxy = get_proxy_string()
    account_tokens = await get_account_tokens()
    
    if not account_tokens:
        logger.error("❌ No account tokens available")
        return
    
    logger.info(f"✅ Using account token: {account_tokens[0][:20]}...")
    if proxy:
        parts = proxy.split(':')
        logger.info(f"✅ Using proxy: {parts[0]}:{parts[1]} (User: {parts[2]})")
    
    # Test the exact endpoint from chart_token_manager.py
    TOKEN_ENDPOINT = 'https://34.102.211.90/chart.js'
    
    # Test data
    test_cases = [
        {
            'name': 'Current Event',
            'payload': {
                'chartKey': '06744f62-aa66-4c18-845a-b3baddf41402',
                'event': 'al-fateh-vs-al-fayha-spl-25-26-m-w-1',
                'expiry': 15
            }
        },
        {
            'name': 'Different Expiry',
            'payload': {
                'chartKey': '06744f62-aa66-4c18-845a-b3baddf41402',
                'event': 'al-fateh-vs-al-fayha-spl-25-26-m-w-1',
                'expiry': 10
            }
        },
        {
            'name': 'Minimal Payload',
            'payload': {
                'chartKey': '06744f62-aa66-4c18-845a-b3baddf41402',
                'event': 'al-fateh-vs-al-fayha-spl-25-26-m-w-1'
            }
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n🧪 Test {i}: {test_case['name']}")
        logger.info("-" * 40)
        
        result = {
            'test_name': test_case['name'],
            'payload': test_case['payload'],
            'success': False,
            'status_code': None,
            'response_body': None,
            'timing_ms': None,
            'error': None
        }
        
        try:
            # Headers for token generation
            headers = {
                'Host': 'cdn-eu.seatsio.net',
                'accept': 'application/json',
                'authorization': f'Bearer {account_tokens[0]}',
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            logger.info(f"📤 Payload: {json.dumps(test_case['payload'], indent=2)}")
            
            # Make request
            start_time = time.perf_counter()
            response = await async_make_request(
                'POST', TOKEN_ENDPOINT,
                proxy=proxy,
                json=test_case['payload'],
                headers=headers
            )
            end_time = time.perf_counter()
            
            timing_ms = (end_time - start_time) * 1000
            
            result['status_code'] = response.status_code
            result['response_body'] = response.text
            result['timing_ms'] = f"{timing_ms:.2f}"
            
            logger.info(f"📥 Status: {response.status_code}")
            logger.info(f"⏱️  Timing: {timing_ms:.2f}ms")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info(f"📄 Response: {json.dumps(data, indent=2)}")
                    
                    if 'holdToken' in data:
                        result['success'] = True
                        result['hold_token'] = data['holdToken']
                        logger.info(f"✅ Token generated successfully: {data['holdToken'][:8]}...")
                        
                        # Test token validity by checking its format
                        token = data['holdToken']
                        if len(token) == 36 and token.count('-') == 4:
                            logger.info("✅ Token format looks valid (UUID format)")
                        else:
                            logger.warning(f"⚠️ Token format unusual: {len(token)} chars, {token.count('-')} dashes")
                    else:
                        result['error'] = f"No holdToken in response: {data}"
                        logger.error(f"❌ {result['error']}")
                        
                except json.JSONDecodeError as e:
                    result['error'] = f"Failed to parse JSON response: {e}"
                    logger.error(f"❌ {result['error']}")
                    logger.error(f"Raw response: {response.text[:500]}")
            else:
                result['error'] = f"HTTP {response.status_code}: {response.text[:200]}"
                logger.error(f"❌ {result['error']}")
                
                # Log full response for debugging
                logger.error(f"Full response: {response.text}")
                
        except Exception as e:
            result['error'] = f"Exception: {str(e)}"
            logger.error(f"❌ Exception: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
        
        results.append(result)
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    # Print summary
    logger.info("\n" + "=" * 60)
    logger.info("🎯 TOKEN ENDPOINT TEST SUMMARY")
    logger.info("=" * 60)
    
    successful_tests = 0
    
    for result in results:
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        logger.info(f"\n🧪 {result['test_name']}: {status}")
        logger.info(f"   Status Code: {result['status_code']}")
        logger.info(f"   Timing: {result.get('timing_ms', 'N/A')}ms")
        
        if result['success']:
            successful_tests += 1
            logger.info(f"   Token: {result.get('hold_token', 'N/A')[:8]}...")
        elif result['error']:
            logger.info(f"   Error: {result['error']}")
    
    logger.info(f"\n📊 Overall: {successful_tests}/{len(results)} tests passed")
    
    if successful_tests == 0:
        logger.error("❌ ALL TESTS FAILED - TOKEN ENDPOINT IS NOT WORKING")
        logger.error("🔍 Possible issues:")
        logger.error("   - Incorrect endpoint URL")
        logger.error("   - Invalid account token")
        logger.error("   - Proxy configuration issues")
        logger.error("   - Network connectivity problems")
        logger.error("   - Server-side issues")
    elif successful_tests < len(results):
        logger.warning(f"⚠️ PARTIAL SUCCESS - {len(results) - successful_tests} tests failed")
        logger.warning("🔍 Some payload configurations may be invalid")
    else:
        logger.info("✅ ALL TESTS PASSED - TOKEN ENDPOINT IS WORKING CORRECTLY")
    
    # Save results
    with open('token_endpoint_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"\n💾 Detailed results saved to token_endpoint_results.json")
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_token_endpoint())
