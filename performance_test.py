#!/usr/bin/env python3
"""
Performance Test for Auto-Hold System
Tests the improvements made to reduce Decision→Request delays
"""

import time
import asyncio
import logging
from typing import List
from auto_hold import AutoHoldSystem, set_debug_mode

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceTest:
    """Test class for measuring auto-hold performance improvements"""
    
    def __init__(self):
        self.results = []
        self.success_count = 0
        self.failure_count = 0
        
    def success_callback(self, seat_id: str, token: str):
        """Handle successful seat holds"""
        self.success_count += 1
        logger.info(f"✅ SUCCESS: {seat_id} held with token {token[:8]}...")
        
    def failure_callback(self, seat_id: str, error: str):
        """Handle failed seat holds"""
        self.failure_count += 1
        logger.info(f"❌ FAILED: {seat_id} - {error}")

    def test_concurrent_holds(self, seat_count: int = 50, debug_mode: bool = False):
        """Test concurrent seat holding performance"""
        logger.info(f"🚀 Starting performance test with {seat_count} seats")
        logger.info(f"🔍 Debug mode: {'ENABLED' if debug_mode else 'DISABLED'}")
        
        # Reset counters
        self.success_count = 0
        self.failure_count = 0
        
        # Create auto-hold system
        auto_hold = AutoHoldSystem(
            event_key="test-event-key",
            channel_keys=["test-channel"],
            team_id="test-team",
            proxy=None
        )
        
        # Set debug mode
        auto_hold.set_debug_mode(debug_mode)
        
        # Set callbacks
        auto_hold.set_callbacks(
            success_callback=self.success_callback,
            failure_callback=self.failure_callback
        )
        
        # Prepare tokens
        logger.info("⚡ Preparing tokens...")
        auto_hold.prepare_tokens_for_seats(seat_count)
        
        # Wait a moment for token preparation
        time.sleep(2)
        
        # Generate test seat IDs
        seat_ids = [f"TEST-{i:03d}" for i in range(1, seat_count + 1)]
        
        # Record start time
        start_time = time.perf_counter()
        
        # Submit all holds concurrently
        logger.info(f"🎯 Submitting {seat_count} concurrent hold requests...")
        for seat_id in seat_ids:
            auto_hold.hold_seat(seat_id, websocket_timestamp=start_time)
        
        # Wait for completion (give it some time)
        logger.info("⏳ Waiting for completion...")
        time.sleep(10)  # Wait 10 seconds for all operations to complete
        
        # Record end time
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        # Get performance stats
        stats = auto_hold.get_performance_stats()
        
        # Log results
        logger.info("=" * 60)
        logger.info("🎯 PERFORMANCE TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"📊 Total Seats Requested: {seat_count}")
        logger.info(f"✅ Successful Holds: {self.success_count}")
        logger.info(f"❌ Failed Holds: {self.failure_count}")
        logger.info(f"⏱️  Total Test Time: {total_time:.2f}s")
        logger.info(f"🚀 Average Time per Seat: {(total_time / seat_count) * 1000:.2f}ms")
        
        if stats:
            logger.info(f"📈 System Stats:")
            logger.info(f"   🎯 Total Attempts: {stats.get('attempts', 0)}")
            logger.info(f"   ✅ Success Rate: {stats.get('success_rate', 0):.1f}%")
            logger.info(f"   ⏱️  Avg Total Time: {stats.get('avg_total_time_ms', 0):.2f}ms")
            logger.info(f"   📡 WebSocket→Decision: {stats.get('avg_ws_to_decision_ms', 0):.2f}ms")
            logger.info(f"   🔄 Decision→Request: {stats.get('avg_decision_to_request_ms', 0):.2f}ms")
            logger.info(f"   🌐 Request→Response: {stats.get('avg_request_to_response_ms', 0):.2f}ms")
        
        logger.info("=" * 60)
        
        # Cleanup
        auto_hold.cleanup()
        
        return {
            'total_seats': seat_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'total_time': total_time,
            'avg_time_per_seat_ms': (total_time / seat_count) * 1000,
            'stats': stats
        }

def main():
    """Run performance tests"""
    test = PerformanceTest()
    
    logger.info("🚀 Auto-Hold Performance Test Suite")
    logger.info("=" * 60)
    
    # Test 1: Small batch without debug
    logger.info("TEST 1: 20 seats without debug mode")
    result1 = test.test_concurrent_holds(seat_count=20, debug_mode=False)
    
    time.sleep(5)  # Brief pause between tests
    
    # Test 2: Medium batch without debug
    logger.info("TEST 2: 50 seats without debug mode")
    result2 = test.test_concurrent_holds(seat_count=50, debug_mode=False)
    
    time.sleep(5)  # Brief pause between tests
    
    # Test 3: Small batch with debug (to show debug functionality)
    logger.info("TEST 3: 10 seats WITH debug mode (showing debug output)")
    result3 = test.test_concurrent_holds(seat_count=10, debug_mode=True)
    
    # Summary
    logger.info("🏁 ALL TESTS COMPLETED")
    logger.info("=" * 60)
    logger.info("Key improvements implemented:")
    logger.info("✅ Increased thread pool from 50 to 500 workers")
    logger.info("✅ Removed debug logging from hot paths")
    logger.info("✅ Added debug mode toggle for troubleshooting")
    logger.info("✅ Optimized token allocation flow")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
