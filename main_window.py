# main_window.py - Refactored and modularized version
import time
import logging
from collections import defaultdict
from threading import Lock
from PyQt5.QtWidgets import QMain<PERSON>indow, QMessageBox, QInputDialog
from PyQt5.QtCore import QEvent, QTimer, QThreadPool, pyqtSignal, QTime, QThread
import threading
import json
import asyncio
import warnings

# Suppress non-critical warnings and errors
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*Event loop is closed.*")
warnings.filterwarnings("ignore", message=".*NoneType.*has no attribute.*send.*")

# Import modular components
from ui_components import UIComponents
from event_handlers import EventHandlers
from seat_processor import SeatProcessor
from system_initializer import SystemInitializer
from booking_operations import BookingOperations

# Your existing helpers
from helper import (
    release_seat,
    group_tickets_by_type_and_status,
    get_event_seatsio_info,
    get_object_statuses
)
from token_retrieval import get_cached_event_id, cache_event_id
from webook_client import <PERSON>ook<PERSON>lient
from manager.proxy_manager import get_global_proxy_manager

# Manager, threads, tabs, workers
from threads.time_left_updater_thread import TimeLeftUpdaterThread
from script_info import SCRIPT_NAME, VERSION

logger = logging.getLogger("webook_pro")

# Suppress specific error messages that don't affect functionality
class ErrorSuppressingFilter(logging.Filter):
    def filter(self, record):
        # Suppress event loop and connection errors that don't affect functionality
        suppressed_messages = [
            "Event loop is closed",
            "NoneType' object has no attribute 'send'",
            "Connection pool is closed",
            "SSL connection has been closed"
        ]
        return not any(msg in record.getMessage() for msg in suppressed_messages)

# Apply filter to reduce noise
logger.addFilter(ErrorSuppressingFilter())


def perform_hwid_check(username: str):
    """
    Stub function. Replace with real logic if needed.
    """
    return True


class MainWindow(QMainWindow):
    # Signal for event loading completion
    event_loaded_signal = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{SCRIPT_NAME} v{VERSION}")
        self.resize(1000, 600)

        # Connect the signal to the finalization method
        self.event_loaded_signal.connect(self._finalize_event_loading)

        # Initialize data structures
        self.tickets_info = defaultdict(lambda: defaultdict(dict))  # type -> status -> {seat_id: seat_data}
        self.seat_id_map = {}  # seat_id: {'type': str, 'status': str}

        # Initialize proxy config
        self.proxy_config = {
            "enabled": False,
            "domain": "p.webshare.io",
            "port": "80",
            "username": "taplmftg-rotate",
            "password": "P@SSWORD"
        }

        # Initialize modular components (without connecting signals yet)
        self.ui_components = UIComponents(self)
        self.seat_processor = SeatProcessor(self)
        self.system_initializer = SystemInitializer(self)
        self.booking_operations = BookingOperations(self)

        # Setup UI components first
        self.ui_components.setup_main_tabs()

        # Now initialize event handlers and connect signals after UI is ready
        self.event_handlers = EventHandlers(self)

        # Initialize chart token refresher
        self.system_initializer.initialize_chart_token()

        # Core system components
        self.token_system = None
        self.simple_hold_manager = None
        self.websocket_manager = None

        # Threading and concurrency management
        self.booking_lock = Lock()
        self.data_lock = Lock()
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(4)

        # Initialize updater thread
        self.time_left_updater = TimeLeftUpdaterThread(interval=1)
        self.time_left_updater.setObjectName("TimeLeftUpdaterThread")
        self.time_left_updater.update_signal.connect(self.held_seats_tab.auto_refresh_held_seats)
        self.time_left_updater.refresh_signal.connect(self.refresh_all_token_times)
        self.time_left_updater.start()

        # Auto-hold tracking
        self.pending_seats = 0
        self.auto_held_seats = {}

    #------------------------------------------------------------------
    # Core initialization and cleanup methods
    #------------------------------------------------------------------

    #------------------------------------------------------------------
    # Token and seat event handlers
    #------------------------------------------------------------------

    def _on_tickets_editing_finished(self):
        """Handle when user finishes editing the tickets input field - PREPARE BATCH TOKENS"""
        try:
            text = self.total_tickets_edit.text().strip()
            if not text:
                return

            # Parse the number
            try:
                num_seats = int(text)
                if num_seats <= 0:
                    return
            except ValueError:
                return

            # Use the new batch holding preparation system
            if hasattr(self, 'auto_hold_system') and self.auto_hold_system:
                self.log(f"🚀 BATCH PREP: Preparing tokens for {num_seats} seats (high concurrency mode)...")

                # Calculate required tokens (fewer tokens, more concurrent requests per token)
                required_tokens = min(20, (num_seats + 49) // 50)  # Up to 20 tokens, 50 seats max per token
                self.log(f"🎯 BATCH PREP: Need {required_tokens} tokens for {num_seats} seats")

                # Prepare tokens using the optimized batch system
                self.auto_hold_system.prepare_tokens_for_seats(num_seats)

                # Also prepare via preparation tab if available
                if hasattr(self, 'preparation_tab'):
                    self.preparation_tab.prepare_for_seats(num_seats)
            else:
                self.log(f"⚠️ Auto-hold system not available for batch preparation")

        except Exception as e:
            # Log errors but don't disrupt the user
            self.log(f"⚠️ Error in batch token preparation: {str(e)}")

    def _on_auto_hold_status_update(self, status):
        """Handle status updates from the auto-hold system"""
        try:
            if status['ready']:
                # All tokens ready
                pass
            elif status['generation_in_progress']:
                # Tokens being generated
                pass
        except Exception as e:
            logger.error(f"❌ Error in auto-hold status update: {e}")

    def on_async_seat_held(self, seat_number, token):
        """
        Handler for seats held by the async hold system.
        """
        # Delegate to seat processor
        self.seat_processor.on_async_seat_held(seat_number, token)


    def initialize_channel_key_refresh_timer(self):
        """Initialize timer for checking channel key updates"""
        self.channel_key_timer = QTimer()
        self.channel_key_timer.timeout.connect(self.check_channel_key_updates)
        
        # Calculate time until next half-hour mark
        current_time = QTime.currentTime()
        current_minute = current_time.minute()
        current_second = current_time.second()
        
        # Calculate milliseconds until next check time (start of hour or half-hour)
        if current_minute < 30:
            # Time until 30-minute mark
            minutes_to_wait = 30 - current_minute
        else:
            # Time until next hour
            minutes_to_wait = 60 - current_minute
        
        # Subtract seconds already elapsed in the current minute
        seconds_to_wait = minutes_to_wait * 60 - current_second
        msecs_to_wait = seconds_to_wait * 1000
        
        # Start one-shot timer for first alignment to hour/half-hour
        QTimer.singleShot(msecs_to_wait, self.start_periodic_channel_key_check)
        
        self.log("🔄 Channel key refresh timer initialized. First check in " + 
                f"{minutes_to_wait} minutes and {current_second} seconds")

    def start_periodic_channel_key_check(self):
        """Start the periodic channel key check timer"""
        # Now we're aligned to hour/half-hour, start the regular 30-minute timer
        self.channel_key_timer.start(30 * 60 * 1000)  # 30 minutes in milliseconds
        
        # Also perform the first check immediately
        self.check_channel_key_updates()

    def check_channel_key_updates(self):
        """Check for updates to channel keys and reload if necessary"""
        if not hasattr(self, 'webook_data') or not self.webook_data:
            self.log("⚠️ No event loaded, skipping channel key check")
            return
        
        try:
            # Get current time for logging
            current_time = QTime.currentTime()
            time_str = current_time.toString("HH:mm:ss")
            self.log(f"🔍 {time_str} - Scheduled channel key check running...")
            
            # Use the enhanced reload_event_data method
            event_key = self.webook_data['data']['seats_io']['event_key']
            success = self.reload_event_data(event_key)
            
            if success:
                self.log(f"✓ {time_str} - Channel key check completed successfully")
                
                # Update the window title to reflect any changes in held seats
                self._update_window_title()
                
                # Force refresh of held seats tab to ensure accurate data
                if hasattr(self, 'held_seats_tab'):
                    self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
                
                # Update ticket type selection tab to ensure accurate data
                if hasattr(self, 'ticket_type_tab'):
                    self.ticket_type_tab.update_tickets_info(self.tickets_info)
            else:
                self.log(f"⚠️ {time_str} - Channel key check failed, will retry at next scheduled time")
                
        except Exception as e:
            self.log(f"❌ Error in scheduled channel key check: {str(e)}")
            logger.error(f"Error in scheduled channel key check: {str(e)}", exc_info=True)



    def on_seats_removed(self, expired_seats):
        """Handle seats that have been removed due to expiration"""
        if not expired_seats:
            return

        # Log the removals
        self.log(f"⚠️ {len(expired_seats)} seats removed due to token expiration")

        # Update the seat data structures
        with self.data_lock:
            for seat_id in expired_seats:
                # Remove from auto_held_seats if it's there
                self.auto_held_seats.pop(seat_id, None)

                # Update seat status in tickets_info if we know about it
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']

                    # Remove from old status dictionary
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)

                    # Update seat status to unknown (it might become free again)
                    self.seat_id_map[seat_id]['status'] = 'unknown'

        # Update the UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    def on_seat_held(self, seat_id, token_id):
        """Handle new seat successfully held by TokenManagementSystem"""
        with self.data_lock:
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats = max(0, self.pending_seats - 1)

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_seat_released(self, seat_id):
        """Handle seat released by TokenManagementSystem"""
        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_seat_transferred(self, seat_id, token_id, success):
        """Handle seat transfer result from TokenManagementSystem"""
        if success:
            self.log(f"Seat {seat_id} transferred to token {token_id}")
            with self.data_lock:
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)
                    self.seat_id_map[seat_id]['status'] = 'transferred'
        else:
            self.log(f"Failed to transfer seat {seat_id} to token {token_id}")
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_seats_expired(self, seat_ids):
        """Handle seats expired in TokenManagementSystem"""
        self.log(f"⚠️ {len(seat_ids)} seats expired")

        # Update seat status in tickets_info
        with self.data_lock:
            for seat_id in seat_ids:
                # Update seat status in tickets_info if we know about it
                if seat_id in self.seat_id_map:
                    seat_type = self.seat_id_map[seat_id]['type']
                    old_status = self.seat_id_map[seat_id]['status']

                    # Remove from old status dictionary
                    if seat_type in self.tickets_info and old_status in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][old_status].pop(seat_id, None)

                    # Update seat status to unknown
                    self.seat_id_map[seat_id]['status'] = 'unknown'

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

        # Update window title
        self._update_window_title()

    def on_simple_hold_seat_held(self, seat_id: str, token_id: str):
        """Handle seat held by SimpleHoldManager"""
        with self.data_lock:
            # Remove from auto_held_seats to allow holding other seats
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats = max(0, self.pending_seats - 1)

        # Register with TokenManagementSystem if available
        if self.token_system:
            self.token_system.register_held_seat(seat_id, token_id)

        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_simple_hold_seat_released(self, seat_id: str):
        """Handle seat released by SimpleHoldManager"""
        # Update UI
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        self._update_window_title()

    def on_auto_hold_success(self, seat_id: str, token: str):
        """Handle successful seat hold from auto-hold system"""
        with self.data_lock:
            # Remove from auto_held_seats to allow holding other seats
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats = max(0, self.pending_seats - 1)

            # Track batch booking results
            if hasattr(self, '_batch_booking_results'):
                self._batch_booking_results['success'] += 1

        # Register with TokenManagementSystem if available (with error handling)
        if self.token_system:
            try:
                self.token_system.register_held_seat(seat_id, token)
            except Exception as e:
                # Don't let token registration errors crash the batch operation
                self.log(f"⚠️ Token registration error for {seat_id}: {str(e)}")

        # Don't log individual successes during batch operations to reduce spam
        # The batch completion will show the final results

    def on_auto_hold_failure(self, seat_id: str, error: str):
        """Handle failed seat hold from auto-hold system"""
        with self.data_lock:
            # Remove from auto_held_seats to allow retrying
            self.auto_held_seats.pop(seat_id, None)
            self.pending_seats = max(0, self.pending_seats - 1)

            # Track batch booking results
            if hasattr(self, '_batch_booking_results'):
                self._batch_booking_results['failed'] += 1

        # Don't log individual failures during batch operations to reduce spam
        # Don't trigger individual fallback attempts - batch retry will handle this

    def on_performance_signal(self, stats: dict):
        """Handle performance statistics from SimpleHoldManager"""
        # Update performance stats if we have a performance monitor
        if hasattr(self, 'performance_stats'):
            # Update our performance stats with the new data
            if 'average_response_time' in stats:
                self.performance_stats.record_network_time(stats['average_response_time'])

        # Log performance info occasionally
        if stats.get('holds_attempted', 0) % 10 == 0 and stats.get('holds_attempted', 0) > 0:
            success_rate = (stats.get('holds_successful', 0) / stats.get('holds_attempted', 1)) * 100
            avg_time = stats.get('average_response_time', 0)
            self.log(f"📊 Hold performance: {success_rate:.1f}% success rate, {avg_time:.1f}ms avg response time")

    def _update_window_title(self):
        """Update window title with current held seat count"""
        try:
            # Only use token_system for seat count
            held_seats_count = 0
            if self.token_system:
                held_seats_count = len(self.token_system.get_all_seats())
            else:
                return  # Don't update if no token system

            current_title = self.windowTitle()
            if '(' in current_title and 'Auto-held:' in current_title:
                base_title = current_title.split('(')[0].strip()
                self.setWindowTitle(f"{base_title} (Auto-held: {held_seats_count})")
        except Exception as e:
            logger.error(f"Error updating window title: {str(e)}")


    def _release_excess_seat(self, seat_number, token):
        """Release excess seat using async method"""
        self.release_seat_sync(seat_number, token)
        self.log(f"Releasing excess seat: {seat_number}")
    #------------------------------------------------------------------
    # Seat data processing and auto-hold logic
    #------------------------------------------------------------------



    def process_single_seat_update(self, seat_data):
        """
        Delegate seat update processing to the seat processor.
        """
        self.seat_processor.process_single_seat_update(seat_data)


    # Seat holding methods moved to SeatProcessor class

    def _try_fallback_hold(self, seat_id):
        """Fallback async hold method"""
        def hold_async():
            try:
                result = self.run_async(self._hold_seat_async(seat_id))
                if result:
                    # Schedule UI update on main thread
                    QTimer.singleShot(0, lambda: self.on_async_seat_held(seat_id, result))
                else:
                    QTimer.singleShot(0, lambda: self.log(f"❌ Failed to hold seat {seat_id}"))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.log(f"❌ Error holding seat {seat_id}: {str(e)}"))

        # Run in background thread
        threading.Thread(target=hold_async, daemon=True).start()
        return True

    async def _hold_seat_async(self, seat_id: str):
        """Async method to hold a seat - fallback when auto-hold is not available"""
        try:
            if not hasattr(self, 'event_key') or not self.event_key:
                logger.error("No event key available for holding seat")
                return None

            # Try to use the token system to hold the seat directly
            if self.token_system:
                # Queue a hold operation through the token system
                success = self.token_system.hold_seat(seat_id)
                if success:
                    # Return a placeholder token ID since the actual token is managed internally
                    return "token_managed_internally"
                else:
                    logger.error(f"Token system failed to hold seat {seat_id}")
                    return None
            else:
                logger.error("No token system available for fallback hold")
                return None

        except Exception as e:
            logger.error(f"Error in async seat hold: {str(e)}")
            return None

    async def _release_seat_async(self, seat_id: str, hold_token: str):
        """Async method to release a seat"""
        try:
            if not hasattr(self, 'event_key') or not self.event_key:
                logger.error("No event key available for releasing seat")
                return False

            # Release the seat using async helper
            success = await release_seat(
                seat_number=seat_id,
                event_key=self.event_key,
                hold_token=hold_token,
                proxy=self.get_proxy_string()
            )

            return success

        except Exception as e:
            logger.error(f"Error in async seat release: {str(e)}")
            return False

    def release_seat_sync(self, seat_id: str, hold_token: str):
        """Synchronous wrapper for releasing seats"""
        def release_async():
            try:
                result = self.run_async(self._release_seat_async(seat_id, hold_token))
                if result:
                    QTimer.singleShot(0, lambda: self.log(f"✅ Released seat {seat_id}"))
                else:
                    QTimer.singleShot(0, lambda: self.log(f"❌ Failed to release seat {seat_id}"))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.log(f"❌ Error releasing seat {seat_id}: {str(e)}"))

        # Run in background thread
        threading.Thread(target=release_async, daemon=True).start()


    def _process_non_free_seat(self, seat_data):
        """Process non-free seats with lower priority"""
        # Schedule this to run after critical operations
        QTimer.singleShot(50, lambda: self._update_seat_data(seat_data))

    def _process_non_auto_seat(self, seat_data):
        """Process free seats that we're not auto-holding"""
        # Schedule this to run after critical operations
        QTimer.singleShot(10, lambda: self._update_seat_data(seat_data))

    def _update_seat_data(self, seat_data):
        """Update internal seat data structures (non-critical path)"""
        try:
            with self.data_lock:
                seat_id = seat_data['objectLabelOrUuid']
                new_status = seat_data.get('status', 'free').lower()

                # Extract seat type
                seat_parts = seat_id.split('-')
                seat_type = seat_parts[0].strip() if seat_parts else seat_id

                # Get existing seat info
                current_info = self.seat_id_map.get(seat_id)

                if current_info:
                    # Seat already tracked - update status
                    previous_status = current_info['status']
                    if new_status != previous_status:
                        # Remove from old status dictionary
                        if seat_type in self.tickets_info and previous_status in self.tickets_info[seat_type]:
                            self.tickets_info[seat_type][previous_status].pop(seat_id, None)

                        # Add to new status dictionary
                        if seat_type not in self.tickets_info:
                            self.tickets_info[seat_type] = {}
                        if new_status not in self.tickets_info[seat_type]:
                            self.tickets_info[seat_type][new_status] = {}
                        self.tickets_info[seat_type][new_status][seat_id] = seat_data
                        self.seat_id_map[seat_id]['status'] = new_status
                else:
                    # New seat - add to tracking
                    self.seat_id_map[seat_id] = {
                        'type': seat_type,
                        'status': new_status
                    }

                    # Add to status dictionary
                    if seat_type not in self.tickets_info:
                        self.tickets_info[seat_type] = {}
                    if new_status not in self.tickets_info[seat_type]:
                        self.tickets_info[seat_type][new_status] = {}
                    self.tickets_info[seat_type][new_status][seat_id] = seat_data

            # Update UI for this seat type
            self.ticket_type_tab.update_type_row(seat_type)
        except Exception as e:
            logger.error(f"Error updating seat data: {str(e)}")

    #------------------------------------------------------------------
    # Token refresh and management
    #------------------------------------------------------------------

    def refresh_all_token_times(self):
        """
        Refresh the time left for all tokens by calling activate_hold_token
        Now only uses the token system
        """
        logger.debug("refresh_all_token_times called")

        # Use token system, which handles its own refresh
        if self.token_system:
            self.held_seats_tab.auto_refresh_held_seats(True)
            return

        # No token system or tokens - just log a message
        # self.log("No token system initialized. Cannot refresh token times.")



    def on_token_refresh_finished(self, count):
        """Called when all token refreshing is finished"""
        if count > 0:
            self.log(f"Refreshed {count} token times from server")

        # Update the UI times - but only the time columns, not full rebuild
        if hasattr(self, 'held_seats_tab'):
            self.held_seats_tab.update_time_displays_only()

    #------------------------------------------------------------------
    # UI Setup and Event Handlers (delegated to modular components)
    #------------------------------------------------------------------

    def on_configure_proxy(self):
        """Handle proxy configuration button click - delegate to event handlers"""
        self.event_handlers.on_configure_proxy()


    def record_proxy_performance(self, proxy_str, success, response_time_ms):
        """Record proxy performance metrics"""
        proxy_manager = get_global_proxy_manager()
        if proxy_manager and proxy_str:
            proxy_manager.report_result(proxy_str, success, response_time_ms)

    def on_hwid_check(self):
        """Handle hardware ID check button click - delegate to event handlers"""
        self.event_handlers.on_hwid_check()

    async def reload_event_data_and_reconnect(self):
        """Reload event data and reconnect WebSocket when connection is lost - delegate to system initializer"""
        return await self.system_initializer.reload_event_data_and_reconnect()

    # Modify the on_websocket_connected method in main_window.py
    def on_websocket_connected(self, connected):
        """Handle WebSocket connection status changes"""
        if connected:
            self.log("[WebSocket] Connected to event updates.")
        else:
            self.log("[WebSocket] Disconnected from event updates. Will attempt to reconnect...")
            # Schedule a reload attempt after a short delay
            QTimer.singleShot(5000, lambda: self.run_async(self.reload_event_data_and_reconnect()))

    def show_performance_monitor(self):
        """Show the performance monitor dialog"""
        from PyQt5.QtWidgets import QMessageBox

        # Get performance stats from auto-hold system
        if hasattr(self, 'auto_hold_system') and self.auto_hold_system:
            stats = self.auto_hold_system.get_performance_stats()

            # Format stats for display
            stats_text = f"""
Performance Statistics:

Total Operations: {stats.get('total_operations', 0)}
Successful Operations: {stats.get('successful_holds', 0)}
Failed Operations: {stats.get('failed_holds', 0)}
Success Rate: {stats.get('success_rate', 0):.1f}%

Average Response Time: {stats.get('avg_total_time_ms', 0):.2f}ms
Current Speed: {stats.get('current_speed', 0):.1f} seats/sec

Active Operations: {stats.get('active_operations', 0)}
"""

            QMessageBox.information(self, "Performance Monitor", stats_text)
        else:
            QMessageBox.information(self, "Performance Monitor", "Auto-hold system not initialized yet.")

    def on_test_auto_hold(self):
        """Handle test auto hold button click - delegate to event handlers"""
        self.event_handlers.on_test_auto_hold()

    def _on_test_completed(self, results):
        """Handle test completion"""
        self.test_auto_hold_button.setText("Test Auto Hold")
        self.test_auto_hold_button.setEnabled(True)

        if "error" in results:
            self.log(f"❌ Test failed: {results['error']}")
            QMessageBox.critical(self, "Test Failed", f"Test initialization failed:\n{results['error']}")
            return

        # Display results
        total_tests = results.get('total_tests', 0)
        passed_tests = results.get('passed_tests', 0)
        success_rate = results.get('success_rate', 0)

        self.log(f"📊 Test Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")

        # Show detailed results in a message box
        details = []
        for test_name, test_result in results.get("test_results", {}).items():
            status = "✅ PASSED" if test_result["success"] else "❌ FAILED"
            duration = test_result["duration_ms"]
            details.append(f"{test_name}: {status} ({duration:.2f}ms)")
            if not test_result["success"] and test_result.get("error"):
                details.append(f"  Error: {test_result['error']}")

        result_text = "\n".join(details)

        if success_rate >= 80:
            QMessageBox.information(self, "Test Results", f"Auto Hold Test Results:\n\n{result_text}\n\n✅ Overall: SUCCESS")
        else:
            QMessageBox.warning(self, "Test Results", f"Auto Hold Test Results:\n\n{result_text}\n\n⚠️ Overall: PARTIAL SUCCESS")

    def _on_ultra_fast_test_completed(self, result):
        """Handle ultra-fast performance test completion"""
        self.test_auto_hold_button.setText("Test Auto Hold")
        self.test_auto_hold_button.setEnabled(True)

        # Display detailed performance results
        if 'error' not in result:
            test_summary = result.get('test_summary', {})
            response_stats = result.get('response_time_stats', {})
            performance_targets = result.get('performance_targets', {})

            total_tests = test_summary.get('total_tests', 0)
            success_rate = test_summary.get('success_rate', 0)
            avg_response = response_stats.get('average_ms', 0)
            under_100ms_pct = performance_targets.get('under_100ms_percentage', 0)
            target_met = performance_targets.get('target_100ms_met', False)

            self.log(f"⚡ LIGHTNING PERFORMANCE TEST COMPLETED!")
            self.log(f"📊 Tests: {total_tests} | Dispatch Success Rate: {success_rate:.1f}%")
            self.log(f"⚡ LIGHTNING Dispatch Time: {avg_response:.2f}ms | Under 50ms: {under_100ms_pct:.1f}%")

            # Check lightning performance targets
            lightning_data = result.get('performance_targets', {})
            ultra_lightning_met = lightning_data.get('ultra_lightning_target_met', False)
            lightning_met = lightning_data.get('lightning_target_met', False)
            under_50ms_pct = lightning_data.get('under_50ms_percentage', 0)
            under_25ms_pct = lightning_data.get('under_25ms_percentage', 0)
            under_10ms_pct = lightning_data.get('under_10ms_percentage', 0)

            if ultra_lightning_met:
                self.log(f"⚡ ULTRA-LIGHTNING ACHIEVED: >80% dispatches under 10ms!")
            elif lightning_met:
                self.log(f"⚡ LIGHTNING TARGET MET: >95% dispatches under 50ms!")
            else:
                self.log(f"⚠️ Lightning target missed - system needs optimization")

            # Log detailed stats
            p95 = response_stats.get('p95_ms', 0)
            p99 = response_stats.get('p99_ms', 0)
            self.log(f"📈 P95: {p95:.2f}ms | P99: {p99:.2f}ms")
            self.log(f"🎯 Under 10ms: {under_10ms_pct:.1f}% | Under 25ms: {under_25ms_pct:.1f}% | Under 50ms: {under_50ms_pct:.1f}%")

            # Calculate speed improvement over 800ms baseline
            if avg_response > 0:
                improvement_factor = 800.0 / avg_response
                self.log(f"🚀 SPEED IMPROVEMENT: {improvement_factor:.1f}x faster than 800ms baseline!")

            # Show summary in message box
            performance_status = "⚡ ULTRA-LIGHTNING" if ultra_lightning_met else "⚡ LIGHTNING" if lightning_met else "⚠️ NEEDS WORK"

            summary_text = (
                f"⚡ LIGHTNING PERFORMANCE TEST RESULTS:\n\n"
                f"Total Tests: {total_tests}\n"
                f"Dispatch Success Rate: {success_rate:.1f}%\n"
                f"Average Dispatch Time: {avg_response:.2f}ms\n"
                f"Best Single Dispatch: {response_stats.get('min_ms', 0):.2f}ms\n\n"
                f"Performance Breakdown:\n"
                f"• Under 10ms: {under_10ms_pct:.1f}%\n"
                f"• Under 25ms: {under_25ms_pct:.1f}%\n"
                f"• Under 50ms: {under_50ms_pct:.1f}%\n\n"
                f"Status: {performance_status}\n\n"
                f"This measures pure dispatch time - the time to\n"
                f"queue a hold request for processing."
            )

            if avg_response > 0:
                improvement = 800.0 / avg_response
                summary_text += f"\n\nSpeed Improvement: {improvement:.1f}x faster!"

            if target_met:
                QMessageBox.information(self, "Performance Test Results", summary_text)
            else:
                QMessageBox.warning(self, "Performance Test Results", summary_text)

        else:
            error_msg = result.get('error', 'Unknown error')
            self.log(f"❌ Ultra-fast performance test failed: {error_msg}")
            QMessageBox.critical(self, "Test Error", f"Performance test failed:\n{error_msg}")

    def _on_test_error(self, error_message):
        """Handle test error"""
        self.test_auto_hold_button.setText("Test Auto Hold")
        self.test_auto_hold_button.setEnabled(True)
        self.log(f"❌ Test error: {error_message}")
        QMessageBox.critical(self, "Test Error", f"Test failed with error:\n{error_message}")

    def on_about_clicked(self):
        """Handle about button click - delegate to event handlers"""
        self.event_handlers.on_about_clicked()

    def on_auto_hold_success(self, seat_id: str, token: str):
        """Handle successful seat hold from auto-hold system - delegate to seat processor"""
        self.seat_processor.on_auto_hold_success(seat_id, token)

    def on_auto_hold_failure(self, seat_id: str, error: str):
        """Handle failed seat hold from auto-hold system - delegate to seat processor"""
        self.seat_processor.on_auto_hold_failure(seat_id, error)

    def reset_auto_held_seats(self):
        """Reset auto-held seats tracking - delegate to seat processor"""
        self.seat_processor.reset_auto_held_seats()

    def log(self, message: str):
        """Writes to the GUI log and system logger"""
        self.log_text.append(message)
        logger.info(message)

    def on_load_event_info(self):
        """Handle load event info button click - delegate to event handlers"""
        self.event_handlers.on_load_event_info()

    async def _load_event_info_async(self, event_key: str, is_season: bool):
        """Async method to load event information"""
        try:
            # Get event data using WebookClient (synchronous call in async context)
            def get_event_data():
                client = WebookClient(proxy=self.get_proxy_string())
                try:
                    return client.get_event_info(event_key=event_key, is_season=is_season)
                finally:
                    client.close()

            # Run the synchronous WebookClient call in a thread pool
            event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
            if not event_data:
                return None

            # Cache the event ID
            cache_event_id(event_data)

            # Get seats data
            data = event_data["data"]["seats_io"]
            if is_season:
                data['event_key'] = data['season_key']

            # Get additional data concurrently
            event_info_task = get_event_seatsio_info(data, proxy=self.get_proxy_string())
            seats_task = get_object_statuses(data["event_key"], data["chart_key"], proxy=self.get_proxy_string())

            # Wait for both tasks to complete
            data['event_info'], seats = await asyncio.gather(event_info_task, seats_task)

            logger.info(f"Event loading completed: {len(seats)} seats retrieved")
            return {
                'webook_data': event_data,
                'seats': seats,
                'data': data
            }

        except Exception as e:
            logger.error(f"Error in async event loading: {str(e)}")
            return None

    def _finalize_event_loading(self, result):
        """Finalize event loading on the main thread"""
        try:
            logger.info("Starting event loading finalization...")
            self.webook_data = result['webook_data']
            seats = result['seats']
            data = result['data']

            logger.info(f"Finalizing event loading with {len(seats)} seats")
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)
            logger.info(f"Grouped seats into {len(self.tickets_info)} ticket types")

            # Store current event data for auto-hold system (already stored in _initialize_token_management_system)
            # self.event_key and self.channel_keys are set in _initialize_token_management_system
            self.current_chart_key = self.webook_data['data']['seats_io']['chart_key']

            # Populate team combo
            self.team_combo.clear()
            if "home_team" in self.webook_data["data"] and self.webook_data["data"]["home_team"]:
                home_team = self.webook_data["data"]["home_team"]
                self.team_combo.addItem(home_team["name"], home_team["_id"])
            if "away_team" in self.webook_data["data"] and self.webook_data["data"]["away_team"]:
                away_team = self.webook_data["data"]["away_team"]
                self.team_combo.addItem(away_team["name"], away_team["_id"])

            # Build seat map
            self.seat_id_map.clear()
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }

            # Initialize systems using modular components
            self.system_initializer.initialize_websocket(data)
            self.ui_components.update_initial_ui()
            self.system_initializer.initialize_token_management_system()
            self.system_initializer.start_simple_holding_system()

            # Log success
            event_id = get_cached_event_id()
            if event_id:
                self.log(f"✅ Event loaded successfully. Event ID: {event_id}")
            else:
                self.log("✅ Event loaded successfully (no event ID cached)")

        except Exception as e:
            self._handle_loading_error(f"Failed to finalize event loading: {str(e)}", e)

    def _handle_loading_error(self, error_msg: str, exception: Exception):
        """Handle event loading errors"""
        self.log(f"❌ {error_msg}")
        logger.exception("Failed to load event info", exc_info=True)
        QMessageBox.critical(self, "Error", error_msg)

    def on_websocket_connected(self, connected):
        """Handle WebSocket connection status changes"""
        if connected:
            self.log("[WebSocket] Connected to event updates.")
        else:
            self.log("[WebSocket] Disconnected from event updates.")

    def on_token_renewed(self, token_id, time_left):
        """Handle token renewed signal"""
        self.log(f"Token {token_id} renewed with {time_left} seconds remaining")
        # Force UI refresh with rebuild
        self.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)

    # Performance tracking removed with FastHoldManager

    def on_refresh_error(self, error_message):
        """Handle refresh errors from WebSocket or workers"""
        self.log(f"[WebSocket/Worker] Error: {error_message}")
        logger.error(f"[WebSocket/Worker] Error: {error_message}")

    def on_book_seats(self):
        """Handle book seats button click - delegate to booking operations"""
        self.booking_operations.handle_book_seats()

    # Booking methods moved to BookingOperations class

    # More booking methods moved to BookingOperations class

    # Booking helper methods moved to BookingOperations class

    def _start_sequential_booking(self, seat_ids):
        """Fallback sequential booking method"""
        try:
            import threading
            import time

            start_time = time.perf_counter()
            self.log(f"🐌 Starting sequential seat holding of {len(seat_ids)} seats...")

            def sequential_booking():
                success_count = 0
                failed_count = 0

                # Hold seats one by one (no user token needed)
                for i, seat_id in enumerate(seat_ids):
                    try:
                        if self.token_system:
                            # Hold the seat using system tokens
                            hold_success = self.token_system.hold_seat(seat_id)
                            if hold_success:
                                success_count += 1
                            else:
                                failed_count += 1
                        else:
                            failed_count += 1

                        # Update progress every 10 seats
                        if (i + 1) % 10 == 0 or (i + 1) == len(seat_ids):
                            progress = ((i + 1) / len(seat_ids)) * 100
                            QTimer.singleShot(0, lambda p=progress: self.log(f"📈 Sequential Progress: {p:.0f}%"))

                    except Exception as seat_error:
                        failed_count += 1
                        QTimer.singleShot(0, lambda e=seat_error: self.log(f"❌ Seat booking error: {str(e)}"))

                # Completion
                total_time = (time.perf_counter() - start_time) * 1000
                QTimer.singleShot(0, lambda: self.log(f"✅ Sequential seat holding completed in {total_time:.1f}ms"))
                QTimer.singleShot(0, lambda: self.log(f"📊 Results: {success_count} seats held, {failed_count} failed"))
                if success_count > 0:
                    QTimer.singleShot(0, lambda: self.log(f"💡 Tip: Use the transfer function later if you need to move seats to your personal token"))
                QTimer.singleShot(0, lambda: self.book_button.setText("Hold Seats from Selected Types (Fast)"))
                QTimer.singleShot(0, lambda: self.book_button.setEnabled(True))

            # Start sequential booking in background thread
            booking_thread = threading.Thread(target=sequential_booking, daemon=True)
            booking_thread.start()

        except Exception as e:
            self.log(f"❌ Sequential booking also failed: {str(e)}")
            self.book_button.setText("Hold Seats from Selected Types (Fast)")
            self.book_button.setEnabled(True)

    def _get_user_booking_token(self):
        """Get user's personal booking token for transferring seats"""
        # Check if user has entered a booking token in the held seats tab
        if hasattr(self, 'held_seats_tab') and self.held_seats_tab.user_hold_token_edit.text().strip():
            return self.held_seats_tab.user_hold_token_edit.text().strip()

        # If no user token is provided, we could create a new token for the user
        # For now, prompt the user to enter their booking token
        token, ok = QInputDialog.getText(
            self,
            'Booking Token Required',
            'Please enter your personal booking token\n(This will be used to book the selected seats):',
            text=''
        )

        if ok and token.strip():
            # Save the token for future use
            self.held_seats_tab.user_hold_token_edit.setText(token.strip())
            return token.strip()

        return None

    def cancel_booking(self):
        """Cancel the current booking operation - simplified version"""
        self.log("ℹ️ Booking cancelled by user")

    def cancel_booking(self):
        """Cancel the booking process"""
        if hasattr(self, 'booking_worker') and self.booking_worker.isRunning():
            self.log("🛑 Cancelling booking process...")
            self.booking_worker.stop()
            # Restore book button
            if hasattr(self, 'book_button_text'):
                self.book_button.setText(self.book_button_text)
                self.book_button.clicked.disconnect()
                self.book_button.clicked.connect(self.on_book_seats)

    def get_proxy_string(self):
        """Get a proxy string using the proxy manager for optimal selection"""
        # Use proxy manager to get the best proxy
        proxy_manager = get_global_proxy_manager()

        # If proxy is disabled or no proxies available, return None
        if not self.proxy_config.get("enabled", False):
            return None

        # Get the proxy based on the mode
        if self.proxy_config.get("mode", "single") == "single":
            # Single proxy mode - Format manually
            domain = self.proxy_config["domain"]
            port = self.proxy_config["port"]
            user = self.proxy_config["username"]
            pwd = self.proxy_config["password"]

            # Return in format appropriate for the system
            return f"{domain}:{port}:{user}:{pwd}"
        else:
            # List mode - Get best proxy from manager
            proxy_str = proxy_manager.get_proxy()
            return proxy_str


    def reload_event_data(self, event_key=None, force_reload=False):
        """
        Reload event data and update the application state.
        
        Args:
            event_key: Event key to reload, or None to use current event key
            force_reload: Whether to force a reload even if channel keys haven't changed
        
        Returns:
            bool: True if reload was successful, False otherwise
        """
        # Use current event key if none provided
        if not event_key and hasattr(self, 'webook_data') and self.webook_data:
            event_key = self.webook_data['data']['seats_io']['event_key']
        
        if not event_key:
            self.log("❌ No event key available for reload")
            return False
        
        self.log(f"🔄 Reloading event data for {event_key}")
        
        try:
            # Get latest event info using WebookClient
            client = WebookClient(proxy=self.get_proxy_string())
            try:
                new_data = client.get_event_info(event_key=event_key)

                if not new_data:
                    self.log(f'❌ Failed to reload event data')
                    return False
            finally:
                client.close()
            
            # Check if channel keys have changed (if not forcing reload)
            if not force_reload and hasattr(self, 'webook_data') and self.webook_data:
                old_keys = self.webook_data['data']['channel_keys']
                new_keys = new_data['data']['channel_keys']
                
                old_keys_json = json.dumps(old_keys, sort_keys=True)
                new_keys_json = json.dumps(new_keys, sort_keys=True)
                
                if old_keys_json == new_keys_json:
                    self.log("✓ Channel keys are unchanged, skipping reload")
                    return True
                else:
                    self.log("🔄 Channel keys have changed, updating data")
            
            # Update webook data
            self.webook_data = new_data
            
            # Process data
            data = self.webook_data["data"]["seats_io"]
            data['event_info'] = self.run_async(get_event_seatsio_info(data, proxy=self.get_proxy_string()))
            
            # Cache the event ID for token creation
            cache_event_id(self.webook_data)
            
            # Reload seats data
            chart_key = data["chart_key"]
            seats = self.run_async(get_object_statuses(data["event_key"], chart_key, proxy=self.get_proxy_string()))
            self.tickets_info = group_tickets_by_type_and_status(seats, free_only=False)
            
            # Rebuild seat map
            self.seat_id_map.clear()
            for ttype, statuses in self.tickets_info.items():
                for status, seats in statuses.items():
                    for seat in seats:
                        seat_id = seat
                        self.seat_id_map[seat_id] = {
                            'type': ttype,
                            'status': status
                        }
            
            # Update subsystems with new channel keys
            if hasattr(self, 'token_system') and self.token_system:
                self.token_system.channel_keys = self.webook_data['data']['channel_keys']
                
            # Channel keys are now stored directly in the main window
            self.channel_keys = self.webook_data['data']['channel_keys']
            
            # Reload WebSocket if needed
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                # Stop current websocket
                self.websocket_manager.stop()
                # Initialize with fresh data
                self._initialize_websocket(data)
            
            # Update UI
            self._update_initial_ui()
            
            self.log("✅ Event data reloaded successfully")
            return True
            
        except Exception as e:
            self.log(f"❌ Error reloading event data: {str(e)}")
            logger.error(f"Error reloading event data: {str(e)}", exc_info=True)
            return False
        
    def on_booking_finished(self, result):
        """Handle completion of booking worker with booking statistics"""
        # Clean up the worker thread
        sender_worker = self.sender()
        if isinstance(sender_worker, QThread):
            sender_worker.quit()
            sender_worker.wait()
            sender_worker.deleteLater()

        if result and result.get("success", False):
            booked_count = result.get("booked_count", 0)
            token_count = result.get("token_count", 0)
            booked_seats = result.get("booked_seats", {})

            self.log(f"✅ Booked {booked_count} seats with {token_count} tokens")

            # Register all booked seats with the token system
            if self.token_system:
                seat_count = 0
                for token_id, seat_ids in booked_seats.items():
                    for seat_id in seat_ids:
                        self.token_system.register_held_seat(seat_id, token_id)
                        seat_count += 1

                self.log(f"📦 Added {seat_count} seats to token management system")
            else:
                self.log(f"⚠️ Token system not initialized, seats won't be managed automatically")
        else:
            error = result.get("error", "Unknown error") if result else "Unknown error"
            self.log(f"❌ Booking failed: {error}")

        # Restore book button
        if hasattr(self, 'book_button_text'):
            self.book_button.setText(self.book_button_text)
            self.book_button.clicked.disconnect()
            self.book_button.clicked.connect(self.on_book_seats)

        # Refresh seats UI
        self.held_seats_tab.auto_refresh_held_seats()


    def on_release_seats(self):
        """Handle release seats button click - delegate to booking operations"""
        self.booking_operations.handle_release_seats()


    def get_now(self) -> float:
        """Helper so we can easily mock time if needed."""
        return time.time()

    def run_async(self, coro):
        """Run an async coroutine in a new event loop"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    def closeEvent(self, event: QEvent):
        """Clean up resources when window is closed"""
        # Stop threads
        if self.websocket_manager:
            self.websocket_manager.stop()
            self.websocket_manager = None

        if hasattr(self, 'time_left_updater') and self.time_left_updater and self.time_left_updater.isRunning():
            self.time_left_updater.stop()
            self.time_left_updater.wait()


        if hasattr(self, 'token_system') and self.token_system:
            self.token_system.shutdown()

        # Clean up fast token manager
        if hasattr(self, 'fast_token_manager') and self.fast_token_manager:
            self.fast_token_manager.cleanup()

        # Cleanly shut down token renewal timer
        if hasattr(self, 'renewal_timer') and self.renewal_timer.isActive():
            self.renewal_timer.stop()

        # Stop channel key refresh timer
        if hasattr(self, 'channel_key_timer') and self.channel_key_timer.isActive():
            self.channel_key_timer.stop()

        # Let parent handle the rest
        super().closeEvent(event)
